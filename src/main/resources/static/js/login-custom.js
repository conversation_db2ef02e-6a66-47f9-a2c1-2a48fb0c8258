/**
 * Custom Login JavaScript
 * Xử lý chức năng nhớ mật khẩu và các hiệu ứng UI
 */

$(document).ready(function() {
    // Kiểm tra và tải thông tin đã lưu
    loadSavedCredentials();
    
    // X<PERSON> lý form submit
    $('#loginForm').on('submit', function(e) {
        if ($('#rememberPassword').is(':checked')) {
            saveCredentials();
        } else {
            clearSavedCredentials();
        }
        
        // Hiệu ứng loading cho button
        const $btn = $('.btn-login');
        $btn.html('<i class="fa fa-spinner fa-spin"></i> Đang đăng nhập...');
        $btn.prop('disabled', true);
    });
    
    // Xử lý thay đổi checkbox
    $('#rememberPassword').on('change', function() {
        if (!$(this).is(':checked')) {
            clearSavedCredentials();
        }
    });
    
    // Thêm hiệu ứng focus cho input
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).parent().removeClass('focused');
        }
    });
    
    // Xử lý Enter key
    $('.form-control').on('keypress', function(e) {
        if (e.which === 13) {
            $('#loginForm').submit();
        }
    });
    
    // Hiệu ứng typing cho placeholder
    animatePlaceholder();
});

/**
 * Lưu thông tin đăng nhập vào localStorage
 */
function saveCredentials() {
    const username = $('#username').val();
    const password = $('#password').val();
    
    if (username && password) {
        try {
            // Mã hóa đơn giản với key ngẫu nhiên
            const key = 'bcsgd2024';
            const encodedPassword = btoa(password + key);
            
            // Thêm timestamp để kiểm tra thời hạn (7 ngày)
            const expiryTime = new Date().getTime() + (7 * 24 * 60 * 60 * 1000);
            
            localStorage.setItem('rememberedUsername', username);
            localStorage.setItem('rememberedPassword', encodedPassword);
            localStorage.setItem('rememberPassword', 'true');
            localStorage.setItem('rememberExpiry', expiryTime);
            
            console.log('Credentials saved successfully');
        } catch (e) {
            console.error('Error saving credentials:', e);
        }
    }
}

/**
 * Tải thông tin đã lưu từ localStorage
 */
function loadSavedCredentials() {
    try {
        const rememberPassword = localStorage.getItem('rememberPassword');
        const expiryTime = localStorage.getItem('rememberExpiry');
        
        // Kiểm tra thời hạn
        if (expiryTime && new Date().getTime() > parseInt(expiryTime)) {
            clearSavedCredentials();
            return;
        }
        
        if (rememberPassword === 'true') {
            const username = localStorage.getItem('rememberedUsername');
            const encodedPassword = localStorage.getItem('rememberedPassword');
            
            if (username && encodedPassword) {
                try {
                    const key = 'bcsgd2024';
                    const decodedPassword = atob(encodedPassword).replace(key, '');
                    
                    $('#username').val(username);
                    $('#password').val(decodedPassword);
                    $('#rememberPassword').prop('checked', true);
                    
                    // Thêm class focused nếu có giá trị
                    if (username) $('#username').parent().addClass('focused');
                    if (decodedPassword) $('#password').parent().addClass('focused');
                    
                    console.log('Credentials loaded successfully');
                } catch (e) {
                    console.error('Error decoding credentials:', e);
                    clearSavedCredentials();
                }
            }
        }
    } catch (e) {
        console.error('Error loading credentials:', e);
        clearSavedCredentials();
    }
}

/**
 * Xóa thông tin đã lưu khỏi localStorage
 */
function clearSavedCredentials() {
    try {
        localStorage.removeItem('rememberedUsername');
        localStorage.removeItem('rememberedPassword');
        localStorage.removeItem('rememberPassword');
        localStorage.removeItem('rememberExpiry');
        console.log('Credentials cleared');
    } catch (e) {
        console.error('Error clearing credentials:', e);
    }
}

/**
 * Hiệu ứng typing cho placeholder
 */
function animatePlaceholder() {
    const usernameInput = $('#username');
    const passwordInput = $('#password');
    
    // Chỉ chạy animation nếu input trống
    if (!usernameInput.val()) {
        typeWriter(usernameInput, 'Tên đăng nhập', 100);
    }
    
    setTimeout(function() {
        if (!passwordInput.val()) {
            typeWriter(passwordInput, 'Mật khẩu', 100);
        }
    }, 1000);
}

/**
 * Hiệu ứng typewriter cho placeholder
 */
function typeWriter(element, text, speed) {
    let i = 0;
    element.attr('placeholder', '');
    
    function type() {
        if (i < text.length) {
            element.attr('placeholder', element.attr('placeholder') + text.charAt(i));
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

/**
 * Hiển thị thông báo toast
 */
function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast toast-${type}" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; border-radius: 5px; color: white; min-width: 250px;">
            <i class="fa fa-${type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        </div>
    `);
    
    // Thêm màu sắc theo loại
    if (type === 'error') {
        toast.css('background-color', '#d9534f');
    } else if (type === 'success') {
        toast.css('background-color', '#5cb85c');
    } else {
        toast.css('background-color', '#5bc0de');
    }
    
    $('body').append(toast);
    
    // Tự động ẩn sau 3 giây
    setTimeout(function() {
        toast.fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

/**
 * Kiểm tra độ mạnh mật khẩu
 */
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}

/**
 * Xử lý lỗi đăng nhập
 */
function handleLoginError() {
    const $btn = $('.btn-login');
    $btn.html('<i class="fa fa-sign-in"></i> Đăng Nhập');
    $btn.prop('disabled', false);
    
    // Thêm hiệu ứng shake cho form
    $('.login-box-body').addClass('shake');
    setTimeout(function() {
        $('.login-box-body').removeClass('shake');
    }, 500);
}

// Xử lý khi có lỗi đăng nhập (từ server)
$(window).on('load', function() {
    if (window.location.search.includes('error')) {
        handleLoginError();
        showToast('Tên đăng nhập hoặc mật khẩu không đúng!', 'error');
    }
});
