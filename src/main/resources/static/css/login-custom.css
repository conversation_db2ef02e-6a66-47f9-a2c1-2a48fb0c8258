/* Custom Login Styles */
.login-logo a {
    color: #fff;
    text-shadow: 0 2px 3px rgba(0,0,0,.55);
}

.login-box-msg {
    font-size: 20px;
    font-weight: 400;
}

.login-box-body {
    border-top: 4px solid #367fa9;
    background-color: rgba(255,255,255,0.9);
    -webkit-box-shadow: 0 4px 15px rgba(0,0,0,.3);
    -moz-box-shadow: 0 4px 15px rgba(0,0,0,.3);
    box-shadow: 0 4px 15px rgba(0,0,0,.3);
    border-radius: 8px;
    padding: 30px;
    animation: slideInDown 0.5s ease-out;
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    height: 45px;
    border-radius: 5px;
    border: 1px solid #ddd;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #367fa9;
    box-shadow: 0 0 8px rgba(54, 127, 169, 0.3);
}

.btn-login {
    height: 45px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 5px;
    background: linear-gradient(45deg, #367fa9, #4a90c2);
    border: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-login:hover {
    background: linear-gradient(45deg, #2e6a8f, #367fa9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(54, 127, 169, 0.4);
}

.btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.remember-me {
    margin: 15px 0;
}

.remember-me label {
    font-weight: normal;
    cursor: pointer;
    color: #555;
}

.remember-me label:hover {
    color: #367fa9;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.form-control-feedback {
    color: #999;
    font-size: 18px;
}

.has-feedback.focused .form-control-feedback {
    color: #367fa9;
}

.login-box {
    width: 400px;
}

.text-danger {
    animation: shake 0.5s ease-in-out;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Shake animation class */
.shake {
    animation: shake 0.5s ease-in-out;
}

/* Toast notifications */
.toast {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive */
@media (max-width: 480px) {
    .login-box {
        width: 90%;
        margin: 7% auto;
    }
    
    .login-box-body {
        padding: 20px;
    }
    
    .form-control {
        height: 40px;
        font-size: 14px;
    }
    
    .btn-login {
        height: 40px;
        font-size: 14px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .login-box-body {
        background-color: rgba(45, 45, 45, 0.9);
        color: #fff;
    }
    
    .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: #555;
        color: #fff;
    }
    
    .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
    }
    
    .remember-me label {
        color: #ccc;
    }
    
    .remember-me label:hover {
        color: #4a90c2;
    }
}
