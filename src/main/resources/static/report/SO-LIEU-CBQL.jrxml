<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TH-chung" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" uuid="805a4f86-ddca-4a4c-93d0-6d89d1b87143">
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataBcSgd.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="thang_1" class="java.lang.Integer"/>
	<parameter name="thang_2" class="java.lang.Integer"/>
	<parameter name="nam_1" class="java.lang.Integer"/>
	<parameter name="nam_2" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[call THC_CBQL($P{thang_1},$P{nam_1}, $P{thang_2} , $P{nam_2});]]>
	</queryString>
	<field name="CHI_TIEU" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="CHI_TIEU"/>
	</field>
	<field name="THANG_1" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="THANG_1"/>
	</field>
	<field name="THANG_2" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="THANG_2"/>
	</field>
	<field name="FLAG" class="java.lang.Integer"/>
	<variable name="Sum_1" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{FLAG}==1?$F{THANG_1}:0]]></variableExpression>
	</variable>
	<variable name="Sum_2" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{FLAG}==1?$F{THANG_2}:0]]></variableExpression>
	</variable>
	<variable name="Sum_3" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[($F{FLAG}==1?$F{THANG_2}:0)-($F{FLAG}==1?$F{THANG_1}:0)]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="0" y="0" width="195" height="20" uuid="88e2bcfb-7c4f-492c-aa4f-d2cb1d7a2a03">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[CÁN BỘ QUẢN LÝ]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="195" y="0" width="130" height="20" uuid="8964d3bf-3a34-40a4-b981-588522af0354">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Sum_1}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="325" y="0" width="130" height="20" uuid="86797c94-9dd1-48c5-ad18-************">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Sum_2}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="455" y="0" width="100" height="20" uuid="5dd9ad94-99f9-4b27-b86c-af8959dec104">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Sum_3}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="-20" y="0" width="20" height="20" uuid="8eedaa27-0af9-4e0b-9d9c-0bc245420a52"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="555" y="0" width="20" height="20" uuid="13413d84-3ff8-413b-bd34-4f5ae8c7fa42"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="195" y="0" width="130" height="20" uuid="a915e3aa-c317-4728-bb66-b04b37a87b90">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANG_1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="0" width="100" height="20" uuid="09408d35-5d52-480b-b501-41cc31424961">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANG_2}-$F{THANG_1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="325" y="0" width="130" height="20" uuid="7bf1686f-6c8a-4590-a611-838fb4901e4f">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{THANG_2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="195" height="20" uuid="e9bdd39d-ea95-431d-93b6-43714aaf436c">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHI_TIEU}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="-20" y="0" width="20" height="20" uuid="e426ae59-b8ed-4c0c-9601-0112bd9ac0ca"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="555" y="0" width="20" height="20" uuid="6af1eae6-efea-4cbd-811a-3b595546b3fb"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
