package vn.vnpt.camau.toolbc.sgd.web.entity;
// Generated May 28, 2020 3:22:43 PM by Hibernate Tools 5.1.10.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;

/**
 * PgdTh generated by hbm2java
 */
@Entity
@Table(name = "PGD_TH")
public class PgdTh implements java.io.Serializable {

	private Integer id;
	private int idPhongGd;
	private Integer truong;
	private Integer lopK1;
	private Integer lopK2;
	private Integer lopK3;
	private Integer lopK4;
	private Integer lopK5;
	private Integer tsLop;
	private Integer hsK1;
	private Integer hsK2;
	private Integer hsK3;
	private Integer hsK4;
	private Integer hsK5;
	private Integer tsHs;
	private Integer cbql;
	private Integer gv;
	private Integer nv;
	private Integer tsCbGvNv;
	private Integer thang;
	private Integer nam;
	private Date ngayGioCapNhat;
	private Integer guiBcSgd;
	private Integer tsPhong;
	private Integer tsPhongHoc;
	private Integer phKienCo;
	private Integer phBanKienCo;
	private Integer phTam;
	private Integer tsPhongLamViec;
	private Integer plvKienCo;
	private Integer plvBanKienCo;
	private Integer plvTam;
	private Integer plvNhaCongVu;
	private Integer pthTinHoc;
	private Integer pthNgoaiNgu;
	private Integer diemLe;
	private Integer truongHaiBuoi;
	private Integer truongBanTru;
	private Integer lopHaiBuoi;
	private Integer lopBanTru;
	private Integer lopHaiBuoiK1;
	private Integer lopHaiBuoiK2;
	private Integer lopHaiBuoiK3;
	private Integer lopHaiBuoiK4;
	private Integer lopHaiBuoiK5;
	private Integer lopBanTruK1;
	private Integer lopBanTruK2;
	private Integer lopBanTruK3;
	private Integer lopBanTruK4;
	private Integer lopBanTruK5;
	private Integer hsHaiBuoi;
	private Integer hsBanTru;
	private Integer hsHaiBuoiK1;
	private Integer hsHaiBuoiK2;
	private Integer hsHaiBuoiK3;
	private Integer hsHaiBuoiK4;
	private Integer hsHaiBuoiK5;
	private Integer hsBanTruK1;
	private Integer hsBanTruK2;
	private Integer hsBanTruK3;
	private Integer hsBanTruK4;
	private Integer hsBanTruK5;

	public PgdTh() {
	}

	public PgdTh(int idPhongGd) {
		this.idPhongGd = idPhongGd;
	}

	public PgdTh(int idPhongGd, Integer truong, Integer lopK1, Integer lopK2, Integer lopK3, Integer lopK4,
	Integer lopK5, Integer tsLop, Integer hsK1, Integer hsK2, Integer hsK3, Integer hsK4, Integer hsK5,
	Integer tsHs, Integer cbql, Integer gv, Integer nv, Integer tsCbGvNv, Integer thang, Integer nam,
	Date ngayGioCapNhat, Integer guiBcSgd, Integer tsPhong, Integer tsPhongHoc, Integer phKienCo,
	Integer phBanKienCo, Integer phTam, Integer tsPhongLamViec, Integer plvKienCo, Integer plvBanKienCo,
	Integer plvTam, Integer plvNhaCongVu, Integer pthTinHoc, Integer pthNgoaiNgu, Integer diemLe,
	Integer truongHaiBuoi, Integer truongBanTru, Integer lopHaiBuoi, Integer lopBanTru, Integer lopHaiBuoiK1,
	Integer lopHaiBuoiK2, Integer lopHaiBuoiK3, Integer lopHaiBuoiK4, Integer lopHaiBuoiK5, Integer lopBanTruK1,
	Integer lopBanTruK2, Integer lopBanTruK3, Integer lopBanTruK4, Integer lopBanTruK5, Integer hsHaiBuoi,
	Integer hsBanTru, Integer hsHaiBuoiK1, Integer hsHaiBuoiK2, Integer hsHaiBuoiK3, Integer hsHaiBuoiK4,
	Integer hsHaiBuoiK5, Integer hsBanTruK1, Integer hsBanTruK2, Integer hsBanTruK3, Integer hsBanTruK4,
	Integer hsBanTruK5) {
		this.idPhongGd = idPhongGd;
		this.truong = truong;
		this.lopK1 = lopK1;
		this.lopK2 = lopK2;
		this.lopK3 = lopK3;
		this.lopK4 = lopK4;
		this.lopK5 = lopK5;
		this.tsLop = tsLop;
		this.hsK1 = hsK1;
		this.hsK2 = hsK2;
		this.hsK3 = hsK3;
		this.hsK4 = hsK4;
		this.hsK5 = hsK5;
		this.tsHs = tsHs;
		this.cbql = cbql;
		this.gv = gv;
		this.nv = nv;
		this.tsCbGvNv = tsCbGvNv;
		this.thang = thang;
		this.nam = nam;
		this.ngayGioCapNhat = ngayGioCapNhat;
		this.guiBcSgd = guiBcSgd;
		this.tsPhong = tsPhong;
		this.tsPhongHoc = tsPhongHoc;
		this.phKienCo = phKienCo;
		this.phBanKienCo = phBanKienCo;
		this.phTam = phTam;
		this.tsPhongLamViec = tsPhongLamViec;
		this.plvKienCo = plvKienCo;
		this.plvBanKienCo = plvBanKienCo;
		this.plvTam = plvTam;
		this.plvNhaCongVu = plvNhaCongVu;
		this.pthTinHoc = pthTinHoc;
		this.pthNgoaiNgu = pthNgoaiNgu;
		this.diemLe = diemLe;
		this.truongHaiBuoi = truongHaiBuoi;
		this.truongBanTru = truongBanTru;
		this.lopHaiBuoi = lopHaiBuoi;
		this.lopBanTru = lopBanTru;
		this.lopHaiBuoiK1 = lopHaiBuoiK1;
		this.lopHaiBuoiK2 = lopHaiBuoiK2;
		this.lopHaiBuoiK3 = lopHaiBuoiK3;
		this.lopHaiBuoiK4 = lopHaiBuoiK4;
		this.lopHaiBuoiK5 = lopHaiBuoiK5;
		this.lopBanTruK1 = lopBanTruK1;
		this.lopBanTruK2 = lopBanTruK2;
		this.lopBanTruK3 = lopBanTruK3;
		this.lopBanTruK4 = lopBanTruK4;
		this.lopBanTruK5 = lopBanTruK5;
		this.hsHaiBuoi = hsHaiBuoi;
		this.hsBanTru = hsBanTru;
		this.hsHaiBuoiK1 = hsHaiBuoiK1;
		this.hsHaiBuoiK2 = hsHaiBuoiK2;
		this.hsHaiBuoiK3 = hsHaiBuoiK3;
		this.hsHaiBuoiK4 = hsHaiBuoiK4;
		this.hsHaiBuoiK5 = hsHaiBuoiK5;
		this.hsBanTruK1 = hsBanTruK1;
		this.hsBanTruK2 = hsBanTruK2;
		this.hsBanTruK3 = hsBanTruK3;
		this.hsBanTruK4 = hsBanTruK4;
		this.hsBanTruK5 = hsBanTruK5;
}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "ID", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "ID_PHONG_GD", nullable = false)
	public int getIdPhongGd() {
		return this.idPhongGd;
	}

	public void setIdPhongGd(int idPhongGd) {
		this.idPhongGd = idPhongGd;
	}

	@Column(name = "TRUONG")
	public Integer getTruong() {
		return this.truong;
	}

	public void setTruong(Integer truong) {
		this.truong = truong;
	}

	@Column(name = "LOP_K1")
	public Integer getLopK1() {
		return this.lopK1;
	}

	public void setLopK1(Integer lopK1) {
		this.lopK1 = lopK1;
	}

	@Column(name = "LOP_K2")
	public Integer getLopK2() {
		return this.lopK2;
	}

	public void setLopK2(Integer lopK2) {
		this.lopK2 = lopK2;
	}

	@Column(name = "LOP_K3")
	public Integer getLopK3() {
		return this.lopK3;
	}

	public void setLopK3(Integer lopK3) {
		this.lopK3 = lopK3;
	}

	@Column(name = "LOP_K4")
	public Integer getLopK4() {
		return this.lopK4;
	}

	public void setLopK4(Integer lopK4) {
		this.lopK4 = lopK4;
	}

	@Column(name = "LOP_K5")
	public Integer getLopK5() {
		return this.lopK5;
	}

	public void setLopK5(Integer lopK5) {
		this.lopK5 = lopK5;
	}

	@Column(name = "TS_LOP")
	public Integer getTsLop() {
		return this.tsLop;
	}

	public void setTsLop(Integer tsLop) {
		this.tsLop = tsLop;
	}

	@Column(name = "HS_K1")
	public Integer getHsK1() {
		return this.hsK1;
	}

	public void setHsK1(Integer hsK1) {
		this.hsK1 = hsK1;
	}

	@Column(name = "HS_K2")
	public Integer getHsK2() {
		return this.hsK2;
	}

	public void setHsK2(Integer hsK2) {
		this.hsK2 = hsK2;
	}

	@Column(name = "HS_K3")
	public Integer getHsK3() {
		return this.hsK3;
	}

	public void setHsK3(Integer hsK3) {
		this.hsK3 = hsK3;
	}

	@Column(name = "HS_K4")
	public Integer getHsK4() {
		return this.hsK4;
	}

	public void setHsK4(Integer hsK4) {
		this.hsK4 = hsK4;
	}

	@Column(name = "HS_K5")
	public Integer getHsK5() {
		return this.hsK5;
	}

	public void setHsK5(Integer hsK5) {
		this.hsK5 = hsK5;
	}

	@Column(name = "TS_HS")
	public Integer getTsHs() {
		return this.tsHs;
	}

	public void setTsHs(Integer tsHs) {
		this.tsHs = tsHs;
	}

	@Column(name = "CBQL")
	public Integer getCbql() {
		return this.cbql;
	}

	public void setCbql(Integer cbql) {
		this.cbql = cbql;
	}

	@Column(name = "GV")
	public Integer getGv() {
		return this.gv;
	}

	public void setGv(Integer gv) {
		this.gv = gv;
	}

	@Column(name = "NV")
	public Integer getNv() {
		return this.nv;
	}

	public void setNv(Integer nv) {
		this.nv = nv;
	}

	@Column(name = "TS_CB_GV_NV")
	public Integer getTsCbGvNv() {
		return this.tsCbGvNv;
	}

	public void setTsCbGvNv(Integer tsCbGvNv) {
		this.tsCbGvNv = tsCbGvNv;
	}

	@Column(name = "THANG")
	public Integer getThang() {
		return this.thang;
	}

	public void setThang(Integer thang) {
		this.thang = thang;
	}

	@Column(name = "NAM")
	public Integer getNam() {
		return this.nam;
	}

	public void setNam(Integer nam) {
		this.nam = nam;
	}

	@CreationTimestamp
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "NGAY_GIO_CAP_NHAT", length = 19)
	public Date getNgayGioCapNhat() {
		return this.ngayGioCapNhat;
	}

	public void setNgayGioCapNhat(Date ngayGioCapNhat) {
		this.ngayGioCapNhat = ngayGioCapNhat;
	}

	@Column(name = "GUI_BC_SGD")
	public Integer getGuiBcSgd() {
		return this.guiBcSgd;
	}

	public void setGuiBcSgd(Integer guiBcSgd) {
		this.guiBcSgd = guiBcSgd;
	}

	@Column(name = "TS_PHONG")
	public Integer getTsPhong() {
		return this.tsPhong;
	}

	public void setTsPhong(Integer tsPhong) {
		this.tsPhong = tsPhong;
	}

	@Column(name = "TS_PHONG_HOC")
	public Integer getTsPhongHoc() {
		return this.tsPhongHoc;
	}

	public void setTsPhongHoc(Integer tsPhongHoc) {
		this.tsPhongHoc = tsPhongHoc;
	}

	@Column(name = "PH_KIEN_CO")
	public Integer getphKienCo() {
		return this.phKienCo;
	}

	public void setphKienCo(Integer phKienCo) {
		this.phKienCo = phKienCo;
	}

	@Column(name = "PH_BAN_KIEN_CO")
	public Integer getphBanKienCo() {
		return this.phBanKienCo;
	}

	public void setphBanKienCo(Integer phBanKienCo) {
		this.phBanKienCo = phBanKienCo;
	}

	@Column(name = "PH_TAM")
	public Integer getphTam() {
		return this.phTam;
	}

	public void setphTam(Integer phTam) {
		this.phTam = phTam;
	}

	@Column(name = "TS_PHONG_LAM_VIEC")
	public Integer getTsPhongLamViec() {
		return this.tsPhongLamViec;
	}

	public void setTsPhongLamViec(Integer tsPhongLamViec) {
		this.tsPhongLamViec = tsPhongLamViec;
	}

	@Column(name = "PLV_KIEN_CO")
	public Integer getPlvKienCo() {
		return this.plvKienCo;
	}

	public void setPlvKienCo(Integer plvKienCo) {
		this.plvKienCo = plvKienCo;
	}

	@Column(name = "PLV_BAN_KIEN_CO")
	public Integer getPlvBanKienCo() {
		return this.plvBanKienCo;
	}

	public void setPlvBanKienCo(Integer plvBanKienCo) {
		this.plvBanKienCo = plvBanKienCo;
	}

	@Column(name = "PLV_TAM")
	public Integer getPlvTam() {
		return this.plvTam;
	}

	public void setPlvTam(Integer plvTam) {
		this.plvTam = plvTam;
	}

	@Column(name = "PLV_NHA_CONG_VU")
	public Integer getPlvNhaCongVu() {
		return this.plvNhaCongVu;
	}

	public void setPlvNhaCongVu(Integer plvNhaCongVu) {
		this.plvNhaCongVu = plvNhaCongVu;
	}

	@Column(name = "PTH_TIN_HOC")
	public Integer getPthTinHoc() {
		return this.pthTinHoc;
	}

	public void setPthTinHoc(Integer pthTinHoc) {
		this.pthTinHoc = pthTinHoc;
	}

	@Column(name = "PTH_NGOAI_NGU")
	public Integer getPthNgoaiNgu() {
		return this.pthNgoaiNgu;
	}

	public void setPthNgoaiNgu(Integer pthNgoaiNgu) {
		this.pthNgoaiNgu = pthNgoaiNgu;
	}

	@Column(name = "DIEM_LE")
	public Integer getDiemLe() {
		return diemLe;
	}

	public void setDiemLe(Integer diemLe) {
		this.diemLe = diemLe;
	}

	@Column(name = "TRUONG_HAI_BUOI")
	public Integer getTruongHaiBuoi() {
		return truongHaiBuoi;
	}

	public void setTruongHaiBuoi(Integer truongHaiBuoi) {
		this.truongHaiBuoi = truongHaiBuoi;
	}

	@Column(name = "TRUONG_BAN_TRU")
	public Integer getTruongBanTru() {
		return truongBanTru;
	}

	public void setTruongBanTru(Integer truongBanTru) {
		this.truongBanTru = truongBanTru;
	}

	@Column(name = "LOP_HAI_BUOI")
	public Integer getLopHaiBuoi() {
		return lopHaiBuoi;
	}

	public void setLopHaiBuoi(Integer lopHaiBuoi) {
		this.lopHaiBuoi = lopHaiBuoi;
	}

	@Column(name = "LOP_BAN_TRU")
	public Integer getLopBanTru() {
		return lopBanTru;
	}

	public void setLopBanTru(Integer lopBanTru) {
		this.lopBanTru = lopBanTru;
	}

	@Column(name = "LOP_HAI_BUOI_K1")
	public Integer getLopHaiBuoiK1() {
		return lopHaiBuoiK1;
	}

	public void setLopHaiBuoiK1(Integer lopHaiBuoiK1) {
		this.lopHaiBuoiK1 = lopHaiBuoiK1;
	}

	@Column(name = "LOP_HAI_BUOI_K2")
	public Integer getLopHaiBuoiK2() {
		return lopHaiBuoiK2;
	}

	public void setLopHaiBuoiK2(Integer lopHaiBuoiK2) {
		this.lopHaiBuoiK2 = lopHaiBuoiK2;
	}

	@Column(name = "LOP_HAI_BUOI_K3")
	public Integer getLopHaiBuoiK3() {
		return lopHaiBuoiK3;
	}

	public void setLopHaiBuoiK3(Integer lopHaiBuoiK3) {
		this.lopHaiBuoiK3 = lopHaiBuoiK3;
	}

	@Column(name = "LOP_HAI_BUOI_K4")
	public Integer getLopHaiBuoiK4() {
		return lopHaiBuoiK4;
	}

	public void setLopHaiBuoiK4(Integer lopHaiBuoiK4) {
		this.lopHaiBuoiK4 = lopHaiBuoiK4;
	}

	@Column(name = "LOP_HAI_BUOI_K5")
	public Integer getLopHaiBuoiK5() {
		return lopHaiBuoiK5;
	}

	public void setLopHaiBuoiK5(Integer lopHaiBuoiK5) {
		this.lopHaiBuoiK5 = lopHaiBuoiK5;
	}

	@Column(name = "LOP_BAN_TRU_K1")
	public Integer getLopBanTruK1() {
		return lopBanTruK1;
	}

	public void setLopBanTruK1(Integer lopBanTruK1) {
		this.lopBanTruK1 = lopBanTruK1;
	}

	@Column(name = "LOP_BAN_TRU_K2")
	public Integer getLopBanTruK2() {
		return lopBanTruK2;
	}

	public void setLopBanTruK2(Integer lopBanTruK2) {
		this.lopBanTruK2 = lopBanTruK2;
	}

	@Column(name = "LOP_BAN_TRU_K3")
	public Integer getLopBanTruK3() {
		return lopBanTruK3;
	}

	public void setLopBanTruK3(Integer lopBanTruK3) {
		this.lopBanTruK3 = lopBanTruK3;
	}

	@Column(name = "LOP_BAN_TRU_K4")
	public Integer getLopBanTruK4() {
		return lopBanTruK4;
	}

	public void setLopBanTruK4(Integer lopBanTruK4) {
		this.lopBanTruK4 = lopBanTruK4;
	}

	@Column(name = "LOP_BAN_TRU_K5")
	public Integer getLopBanTruK5() {
		return lopBanTruK5;
	}

	public void setLopBanTruK5(Integer lopBanTruK5) {
		this.lopBanTruK5 = lopBanTruK5;
	}

	@Column(name = "HS_HAI_BUOI")
	public Integer getHsHaiBuoi() {
		return hsHaiBuoi;
	}

	public void setHsHaiBuoi(Integer hsHaiBuoi) {
		this.hsHaiBuoi = hsHaiBuoi;
	}

	@Column(name = "HS_BAN_TRU")
	public Integer getHsBanTru() {
		return hsBanTru;
	}

	public void setHsBanTru(Integer hsBanTru) {
		this.hsBanTru = hsBanTru;
	}

	@Column(name = "HS_HAI_BUOI_K1")
	public Integer getHsHaiBuoiK1() {
		return hsHaiBuoiK1;
	}

	public void setHsHaiBuoiK1(Integer hsHaiBuoiK1) {
		this.hsHaiBuoiK1 = hsHaiBuoiK1;
	}

	@Column(name = "HS_HAI_BUOI_K2")
	public Integer getHsHaiBuoiK2() {
		return hsHaiBuoiK2;
	}

	public void setHsHaiBuoiK2(Integer hsHaiBuoiK2) {
		this.hsHaiBuoiK2 = hsHaiBuoiK2;
	}

	@Column(name = "HS_HAI_BUOI_K3")
	public Integer getHsHaiBuoiK3() {
		return hsHaiBuoiK3;
	}

	public void setHsHaiBuoiK3(Integer hsHaiBuoiK3) {
		this.hsHaiBuoiK3 = hsHaiBuoiK3;
	}

	@Column(name = "HS_HAI_BUOI_K4")
	public Integer getHsHaiBuoiK4() {
		return hsHaiBuoiK4;
	}

	public void setHsHaiBuoiK4(Integer hsHaiBuoiK4) {
		this.hsHaiBuoiK4 = hsHaiBuoiK4;
	}

	@Column(name = "HS_HAI_BUOI_K5")
	public Integer getHsHaiBuoiK5() {
		return hsHaiBuoiK5;
	}

	public void setHsHaiBuoiK5(Integer hsHaiBuoiK5) {
		this.hsHaiBuoiK5 = hsHaiBuoiK5;
	}

	@Column(name = "HS_BAN_TRU_K1")
	public Integer getHsBanTruK1() {
		return hsBanTruK1;
	}

	public void setHsBanTruK1(Integer hsBanTruK1) {
		this.hsBanTruK1 = hsBanTruK1;
	}

	@Column(name = "HS_BAN_TRU_K2")
	public Integer getHsBanTruK2() {
		return hsBanTruK2;
	}

	public void setHsBanTruK2(Integer hsBanTruK2) {
		this.hsBanTruK2 = hsBanTruK2;
	}

	@Column(name = "HS_BAN_TRU_K3")
	public Integer getHsBanTruK3() {
		return hsBanTruK3;
	}

	public void setHsBanTruK3(Integer hsBanTruK3) {
		this.hsBanTruK3 = hsBanTruK3;
	}

	@Column(name = "HS_BAN_TRU_K4")
	public Integer getHsBanTruK4() {
		return hsBanTruK4;
	}

	public void setHsBanTruK4(Integer hsBanTruK4) {
		this.hsBanTruK4 = hsBanTruK4;
	}

	@Column(name = "HS_BAN_TRU_K5")
	public Integer getHsBanTruK5() {
		return hsBanTruK5;
	}

	public void setHsBanTruK5(Integer hsBanTruK5) {
		this.hsBanTruK5 = hsBanTruK5;
	}

}
