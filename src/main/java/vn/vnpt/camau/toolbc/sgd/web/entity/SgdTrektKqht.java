package vn.vnpt.camau.toolbc.sgd.web.entity;
// Generated Jun 18, 2020 9:54:48 AM by Hibernate Tools 5.1.10.Final

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * SgdTrektKqht generated by hbm2java
 */
@Entity
@Table(name = "SGD_TREKT_KQHT")
public class SgdTrektKqht implements java.io.Serializable {

	private Integer id;
	private Integer idTruong;
	private String tenTruong;
	private Integer thang;
	private Integer nam;
	private Date ngayGioCapNhat;
	private Integer guiBcSgd;
	private Integer hkTotK6;
	private BigDecimal tlHkTotK6;
	private Integer hkKhaK6;
	private BigDecimal tlHkKhaK6;
	private Integer hkTbK6;
	private BigDecimal tlHkTbK6;
	private Integer hkYeuK6;
	private BigDecimal tlHkYeuK6;
	private Integer hlGioiK6;
	private BigDecimal tlHlGioiK6;
	private Integer hlKhaK6;
	private BigDecimal tlHlKhaK6;
	private Integer hlTbK6;
	private BigDecimal tlHlTbK6;
	private Integer hlYeuK6;
	private BigDecimal tlHlYeuK6;
	private Integer hlKemK6;
	private BigDecimal tlHlKemK6;
	private Integer hkTotK7;
	private BigDecimal tlHkTotK7;
	private Integer hkKhaK7;
	private BigDecimal tlHkKhaK7;
	private Integer hkTbK7;
	private BigDecimal tlHkTbK7;
	private Integer hkYeuK7;
	private BigDecimal tlHkYeuK7;
	private Integer hlGioiK7;
	private BigDecimal tlHlGioiK7;
	private Integer hlKhaK7;
	private BigDecimal tlHlKhaK7;
	private Integer hlTbK7;
	private BigDecimal tlHlTbK7;
	private Integer hlYeuK7;
	private BigDecimal tlHlYeuK7;
	private Integer hlKemK7;
	private BigDecimal tlHlKemK7;
	private Integer hkTotK8;
	private BigDecimal tlHkTotK8;
	private Integer hkKhaK8;
	private BigDecimal tlHkKhaK8;
	private Integer hkTbK8;
	private BigDecimal tlHkTbK8;
	private Integer hkYeuK8;
	private BigDecimal tlHkYeuK8;
	private Integer hlGioiK8;
	private BigDecimal tlHlGioiK8;
	private Integer hlKhaK8;
	private BigDecimal tlHlKhaK8;
	private Integer hlTbK8;
	private BigDecimal tlHlTbK8;
	private Integer hlYeuK8;
	private BigDecimal tlHlYeuK8;
	private Integer hlKemK8;
	private BigDecimal tlHlKemK8;
	private Integer hkTotK9;
	private BigDecimal tlHkTotK9;
	private Integer hkKhaK9;
	private BigDecimal tlHkKhaK9;
	private Integer hkTbK9;
	private BigDecimal tlHkTbK9;
	private Integer hkYeuK9;
	private BigDecimal tlHkYeuK9;
	private Integer hlGioiK9;
	private BigDecimal tlHlGioiK9;
	private Integer hlKhaK9;
	private BigDecimal tlHlKhaK9;
	private Integer hlTbK9;
	private BigDecimal tlHlTbK9;
	private Integer hlYeuK9;
	private BigDecimal tlHlYeuK9;
	private Integer hlKemK9;
	private BigDecimal tlHlKemK9;
	private Integer hkTotK10;
	private BigDecimal tlHkTotK10;
	private Integer hkKhaK10;
	private BigDecimal tlHkKhaK10;
	private Integer hkTbK10;
	private BigDecimal tlHkTbK10;
	private Integer hkYeuK10;
	private BigDecimal tlHkYeuK10;
	private Integer hlGioiK10;
	private BigDecimal tlHlGioiK10;
	private Integer hlKhaK10;
	private BigDecimal tlHlKhaK10;
	private Integer hlTbK10;
	private BigDecimal tlHlTbK10;
	private Integer hlYeuK10;
	private BigDecimal tlHlYeuK10;
	private Integer hlKemK10;
	private BigDecimal tlHlKemK10;
	private Integer hkTotK11;
	private BigDecimal tlHkTotK11;
	private Integer hkKhaK11;
	private BigDecimal tlHkKhaK11;
	private Integer hkTbK11;
	private BigDecimal tlHkTbK11;
	private Integer hkYeuK11;
	private BigDecimal tlHkYeuK11;
	private Integer hlGioiK11;
	private BigDecimal tlHlGioiK11;
	private Integer hlKhaK11;
	private BigDecimal tlHlKhaK11;
	private Integer hlTbK11;
	private BigDecimal tlHlTbK11;
	private Integer hlYeuK11;
	private BigDecimal tlHlYeuK11;
	private Integer hlKemK11;
	private BigDecimal tlHlKemK11;
	private Integer hkTotK12;
	private BigDecimal tlHkTotK12;
	private Integer hkKhaK12;
	private BigDecimal tlHkKhaK12;
	private Integer hkTbK12;
	private BigDecimal tlHkTbK12;
	private Integer hkYeuK12;
	private BigDecimal tlHkYeuK12;
	private Integer hlGioiK12;
	private BigDecimal tlHlGioiK12;
	private Integer hlKhaK12;
	private BigDecimal tlHlKhaK12;
	private Integer hlTbK12;
	private BigDecimal tlHlTbK12;
	private Integer hlYeuK12;
	private BigDecimal tlHlYeuK12;
	private Integer hlKemK12;
	private BigDecimal tlHlKemK12;
	private Integer tsHsThcs;
	private Integer tsHsThpt;
	private Integer hkTotThcs;
	private BigDecimal tlHkTotThcs;
	private Integer hkKhaThcs;
	private BigDecimal tlHkKhaThcs;
	private Integer hkTbThcs;
	private BigDecimal tlHkTbThcs;
	private Integer hkYeuThcs;
	private BigDecimal tlHkYeuThcs;
	private Integer hlGioiThcs;
	private BigDecimal tlHlGioiThcs;
	private Integer hlKhaThcs;
	private BigDecimal tlHlKhaThcs;
	private Integer hlTbThcs;
	private BigDecimal tlHlTbThcs;
	private Integer hlYeuThcs;
	private BigDecimal tlHlYeuThcs;
	private Integer hlKemThcs;
	private BigDecimal tlHlKemThcs;
	private Integer hkTotThpt;
	private BigDecimal tlHkTotThpt;
	private Integer hkKhaThpt;
	private BigDecimal tlHkKhaThpt;
	private Integer hkTbThpt;
	private BigDecimal tlHkTbThpt;
	private Integer hkYeuThpt;
	private BigDecimal tlHkYeuThpt;
	private Integer hlGioiThpt;
	private BigDecimal tlHlGioiThpt;
	private Integer hlKhaThpt;
	private BigDecimal tlHlKhaThpt;
	private Integer hlTbThpt;
	private BigDecimal tlHlTbThpt;
	private Integer hlYeuThpt;
	private BigDecimal tlHlYeuThpt;
	private Integer hlKemThpt;
	private BigDecimal tlHlKemThpt;
	private Integer hsK6;
	private Integer hsK7;
	private Integer hsK8;
	private Integer hsK9;
	private Integer hsK10;
	private Integer hsK11;
	private Integer hsK12;

	public SgdTrektKqht() {
	}

	public SgdTrektKqht(Integer idTruong, String tenTruong, Integer thang, Integer nam, Date ngayGioCapNhat,
			Integer guiBcSgd, Integer hkTotK6, BigDecimal tlHkTotK6, Integer hkKhaK6, BigDecimal tlHkKhaK6,
			Integer hkTbK6, BigDecimal tlHkTbK6, Integer hkYeuK6, BigDecimal tlHkYeuK6, Integer hlGioiK6,
			BigDecimal tlHlGioiK6, Integer hlKhaK6, BigDecimal tlHlKhaK6, Integer hlTbK6, BigDecimal tlHlTbK6,
			Integer hlYeuK6, BigDecimal tlHlYeuK6, Integer hlKemK6, BigDecimal tlHlKemK6, Integer hkTotK7,
			BigDecimal tlHkTotK7, Integer hkKhaK7, BigDecimal tlHkKhaK7, Integer hkTbK7, BigDecimal tlHkTbK7,
			Integer hkYeuK7, BigDecimal tlHkYeuK7, Integer hlGioiK7, BigDecimal tlHlGioiK7, Integer hlKhaK7,
			BigDecimal tlHlKhaK7, Integer hlTbK7, BigDecimal tlHlTbK7, Integer hlYeuK7, BigDecimal tlHlYeuK7,
			Integer hlKemK7, BigDecimal tlHlKemK7, Integer hkTotK8, BigDecimal tlHkTotK8, Integer hkKhaK8,
			BigDecimal tlHkKhaK8, Integer hkTbK8, BigDecimal tlHkTbK8, Integer hkYeuK8, BigDecimal tlHkYeuK8,
			Integer hlGioiK8, BigDecimal tlHlGioiK8, Integer hlKhaK8, BigDecimal tlHlKhaK8, Integer hlTbK8,
			BigDecimal tlHlTbK8, Integer hlYeuK8, BigDecimal tlHlYeuK8, Integer hlKemK8, BigDecimal tlHlKemK8,
			Integer hkTotK9, BigDecimal tlHkTotK9, Integer hkKhaK9, BigDecimal tlHkKhaK9, Integer hkTbK9,
			BigDecimal tlHkTbK9, Integer hkYeuK9, BigDecimal tlHkYeuK9, Integer hlGioiK9, BigDecimal tlHlGioiK9,
			Integer hlKhaK9, BigDecimal tlHlKhaK9, Integer hlTbK9, BigDecimal tlHlTbK9, Integer hlYeuK9,
			BigDecimal tlHlYeuK9, Integer hlKemK9, BigDecimal tlHlKemK9, Integer hkTotK10, BigDecimal tlHkTotK10,
			Integer hkKhaK10, BigDecimal tlHkKhaK10, Integer hkTbK10, BigDecimal tlHkTbK10, Integer hkYeuK10,
			BigDecimal tlHkYeuK10, Integer hlGioiK10, BigDecimal tlHlGioiK10, Integer hlKhaK10, BigDecimal tlHlKhaK10,
			Integer hlTbK10, BigDecimal tlHlTbK10, Integer hlYeuK10, BigDecimal tlHlYeuK10, Integer hlKemK10,
			BigDecimal tlHlKemK10, Integer hkTotK11, BigDecimal tlHkTotK11, Integer hkKhaK11, BigDecimal tlHkKhaK11,
			Integer hkTbK11, BigDecimal tlHkTbK11, Integer hkYeuK11, BigDecimal tlHkYeuK11, Integer hlGioiK11,
			BigDecimal tlHlGioiK11, Integer hlKhaK11, BigDecimal tlHlKhaK11, Integer hlTbK11, BigDecimal tlHlTbK11,
			Integer hlYeuK11, BigDecimal tlHlYeuK11, Integer hlKemK11, BigDecimal tlHlKemK11, Integer hkTotK12,
			BigDecimal tlHkTotK12, Integer hkKhaK12, BigDecimal tlHkKhaK12, Integer hkTbK12, BigDecimal tlHkTbK12,
			Integer hkYeuK12, BigDecimal tlHkYeuK12, Integer hlGioiK12, BigDecimal tlHlGioiK12, Integer hlKhaK12,
			BigDecimal tlHlKhaK12, Integer hlTbK12, BigDecimal tlHlTbK12, Integer hlYeuK12, BigDecimal tlHlYeuK12,
			Integer hlKemK12, BigDecimal tlHlKemK12, Integer tsHsThcs, Integer tsHsThpt, Integer hkTotThcs,
			BigDecimal tlHkTotThcs, Integer hkKhaThcs, BigDecimal tlHkKhaThcs, Integer hkTbThcs, BigDecimal tlHkTbThcs,
			Integer hkYeuThcs, BigDecimal tlHkYeuThcs, Integer hlGioiThcs, BigDecimal tlHlGioiThcs, Integer hlKhaThcs,
			BigDecimal tlHlKhaThcs, Integer hlTbThcs, BigDecimal tlHlTbThcs, Integer hlYeuThcs, BigDecimal tlHlYeuThcs,
			Integer hlKemThcs, BigDecimal tlHlKemThcs, Integer hkTotThpt, BigDecimal tlHkTotThpt, Integer hkKhaThpt,
			BigDecimal tlHkKhaThpt, Integer hkTbThpt, BigDecimal tlHkTbThpt, Integer hkYeuThpt, BigDecimal tlHkYeuThpt,
			Integer hlGioiThpt, BigDecimal tlHlGioiThpt, Integer hlKhaThpt, BigDecimal tlHlKhaThpt, Integer hlTbThpt,
			BigDecimal tlHlTbThpt, Integer hlYeuThpt, BigDecimal tlHlYeuThpt, Integer hlKemThpt, BigDecimal tlHlKemThpt,
			Integer hsK6, Integer hsK7, Integer hsK8, Integer hsK9, Integer hsK10, Integer hsK11, Integer hsK12) {
		this.idTruong = idTruong;
		this.tenTruong = tenTruong;
		this.thang = thang;
		this.nam = nam;
		this.ngayGioCapNhat = ngayGioCapNhat;
		this.guiBcSgd = guiBcSgd;
		this.hkTotK6 = hkTotK6;
		this.tlHkTotK6 = tlHkTotK6;
		this.hkKhaK6 = hkKhaK6;
		this.tlHkKhaK6 = tlHkKhaK6;
		this.hkTbK6 = hkTbK6;
		this.tlHkTbK6 = tlHkTbK6;
		this.hkYeuK6 = hkYeuK6;
		this.tlHkYeuK6 = tlHkYeuK6;
		this.hlGioiK6 = hlGioiK6;
		this.tlHlGioiK6 = tlHlGioiK6;
		this.hlKhaK6 = hlKhaK6;
		this.tlHlKhaK6 = tlHlKhaK6;
		this.hlTbK6 = hlTbK6;
		this.tlHlTbK6 = tlHlTbK6;
		this.hlYeuK6 = hlYeuK6;
		this.tlHlYeuK6 = tlHlYeuK6;
		this.hlKemK6 = hlKemK6;
		this.tlHlKemK6 = tlHlKemK6;
		this.hkTotK7 = hkTotK7;
		this.tlHkTotK7 = tlHkTotK7;
		this.hkKhaK7 = hkKhaK7;
		this.tlHkKhaK7 = tlHkKhaK7;
		this.hkTbK7 = hkTbK7;
		this.tlHkTbK7 = tlHkTbK7;
		this.hkYeuK7 = hkYeuK7;
		this.tlHkYeuK7 = tlHkYeuK7;
		this.hlGioiK7 = hlGioiK7;
		this.tlHlGioiK7 = tlHlGioiK7;
		this.hlKhaK7 = hlKhaK7;
		this.tlHlKhaK7 = tlHlKhaK7;
		this.hlTbK7 = hlTbK7;
		this.tlHlTbK7 = tlHlTbK7;
		this.hlYeuK7 = hlYeuK7;
		this.tlHlYeuK7 = tlHlYeuK7;
		this.hlKemK7 = hlKemK7;
		this.tlHlKemK7 = tlHlKemK7;
		this.hkTotK8 = hkTotK8;
		this.tlHkTotK8 = tlHkTotK8;
		this.hkKhaK8 = hkKhaK8;
		this.tlHkKhaK8 = tlHkKhaK8;
		this.hkTbK8 = hkTbK8;
		this.tlHkTbK8 = tlHkTbK8;
		this.hkYeuK8 = hkYeuK8;
		this.tlHkYeuK8 = tlHkYeuK8;
		this.hlGioiK8 = hlGioiK8;
		this.tlHlGioiK8 = tlHlGioiK8;
		this.hlKhaK8 = hlKhaK8;
		this.tlHlKhaK8 = tlHlKhaK8;
		this.hlTbK8 = hlTbK8;
		this.tlHlTbK8 = tlHlTbK8;
		this.hlYeuK8 = hlYeuK8;
		this.tlHlYeuK8 = tlHlYeuK8;
		this.hlKemK8 = hlKemK8;
		this.tlHlKemK8 = tlHlKemK8;
		this.hkTotK9 = hkTotK9;
		this.tlHkTotK9 = tlHkTotK9;
		this.hkKhaK9 = hkKhaK9;
		this.tlHkKhaK9 = tlHkKhaK9;
		this.hkTbK9 = hkTbK9;
		this.tlHkTbK9 = tlHkTbK9;
		this.hkYeuK9 = hkYeuK9;
		this.tlHkYeuK9 = tlHkYeuK9;
		this.hlGioiK9 = hlGioiK9;
		this.tlHlGioiK9 = tlHlGioiK9;
		this.hlKhaK9 = hlKhaK9;
		this.tlHlKhaK9 = tlHlKhaK9;
		this.hlTbK9 = hlTbK9;
		this.tlHlTbK9 = tlHlTbK9;
		this.hlYeuK9 = hlYeuK9;
		this.tlHlYeuK9 = tlHlYeuK9;
		this.hlKemK9 = hlKemK9;
		this.tlHlKemK9 = tlHlKemK9;
		this.hkTotK10 = hkTotK10;
		this.tlHkTotK10 = tlHkTotK10;
		this.hkKhaK10 = hkKhaK10;
		this.tlHkKhaK10 = tlHkKhaK10;
		this.hkTbK10 = hkTbK10;
		this.tlHkTbK10 = tlHkTbK10;
		this.hkYeuK10 = hkYeuK10;
		this.tlHkYeuK10 = tlHkYeuK10;
		this.hlGioiK10 = hlGioiK10;
		this.tlHlGioiK10 = tlHlGioiK10;
		this.hlKhaK10 = hlKhaK10;
		this.tlHlKhaK10 = tlHlKhaK10;
		this.hlTbK10 = hlTbK10;
		this.tlHlTbK10 = tlHlTbK10;
		this.hlYeuK10 = hlYeuK10;
		this.tlHlYeuK10 = tlHlYeuK10;
		this.hlKemK10 = hlKemK10;
		this.tlHlKemK10 = tlHlKemK10;
		this.hkTotK11 = hkTotK11;
		this.tlHkTotK11 = tlHkTotK11;
		this.hkKhaK11 = hkKhaK11;
		this.tlHkKhaK11 = tlHkKhaK11;
		this.hkTbK11 = hkTbK11;
		this.tlHkTbK11 = tlHkTbK11;
		this.hkYeuK11 = hkYeuK11;
		this.tlHkYeuK11 = tlHkYeuK11;
		this.hlGioiK11 = hlGioiK11;
		this.tlHlGioiK11 = tlHlGioiK11;
		this.hlKhaK11 = hlKhaK11;
		this.tlHlKhaK11 = tlHlKhaK11;
		this.hlTbK11 = hlTbK11;
		this.tlHlTbK11 = tlHlTbK11;
		this.hlYeuK11 = hlYeuK11;
		this.tlHlYeuK11 = tlHlYeuK11;
		this.hlKemK11 = hlKemK11;
		this.tlHlKemK11 = tlHlKemK11;
		this.hkTotK12 = hkTotK12;
		this.tlHkTotK12 = tlHkTotK12;
		this.hkKhaK12 = hkKhaK12;
		this.tlHkKhaK12 = tlHkKhaK12;
		this.hkTbK12 = hkTbK12;
		this.tlHkTbK12 = tlHkTbK12;
		this.hkYeuK12 = hkYeuK12;
		this.tlHkYeuK12 = tlHkYeuK12;
		this.hlGioiK12 = hlGioiK12;
		this.tlHlGioiK12 = tlHlGioiK12;
		this.hlKhaK12 = hlKhaK12;
		this.tlHlKhaK12 = tlHlKhaK12;
		this.hlTbK12 = hlTbK12;
		this.tlHlTbK12 = tlHlTbK12;
		this.hlYeuK12 = hlYeuK12;
		this.tlHlYeuK12 = tlHlYeuK12;
		this.hlKemK12 = hlKemK12;
		this.tlHlKemK12 = tlHlKemK12;
		this.tsHsThcs = tsHsThcs;
		this.tsHsThpt = tsHsThpt;
		this.hkTotThcs = hkTotThcs;
		this.tlHkTotThcs = tlHkTotThcs;
		this.hkKhaThcs = hkKhaThcs;
		this.tlHkKhaThcs = tlHkKhaThcs;
		this.hkTbThcs = hkTbThcs;
		this.tlHkTbThcs = tlHkTbThcs;
		this.hkYeuThcs = hkYeuThcs;
		this.tlHkYeuThcs = tlHkYeuThcs;
		this.hlGioiThcs = hlGioiThcs;
		this.tlHlGioiThcs = tlHlGioiThcs;
		this.hlKhaThcs = hlKhaThcs;
		this.tlHlKhaThcs = tlHlKhaThcs;
		this.hlTbThcs = hlTbThcs;
		this.tlHlTbThcs = tlHlTbThcs;
		this.hlYeuThcs = hlYeuThcs;
		this.tlHlYeuThcs = tlHlYeuThcs;
		this.hlKemThcs = hlKemThcs;
		this.tlHlKemThcs = tlHlKemThcs;
		this.hkTotThpt = hkTotThpt;
		this.tlHkTotThpt = tlHkTotThpt;
		this.hkKhaThpt = hkKhaThpt;
		this.tlHkKhaThpt = tlHkKhaThpt;
		this.hkTbThpt = hkTbThpt;
		this.tlHkTbThpt = tlHkTbThpt;
		this.hkYeuThpt = hkYeuThpt;
		this.tlHkYeuThpt = tlHkYeuThpt;
		this.hlGioiThpt = hlGioiThpt;
		this.tlHlGioiThpt = tlHlGioiThpt;
		this.hlKhaThpt = hlKhaThpt;
		this.tlHlKhaThpt = tlHlKhaThpt;
		this.hlTbThpt = hlTbThpt;
		this.tlHlTbThpt = tlHlTbThpt;
		this.hlYeuThpt = hlYeuThpt;
		this.tlHlYeuThpt = tlHlYeuThpt;
		this.hlKemThpt = hlKemThpt;
		this.tlHlKemThpt = tlHlKemThpt;
		this.hsK6 = hsK6;
		this.hsK7 = hsK7;
		this.hsK8 = hsK8;
		this.hsK9 = hsK9;
		this.hsK10 = hsK10;
		this.hsK11 = hsK11;
		this.hsK12 = hsK12;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "ID", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "ID_TRUONG")
	public Integer getIdTruong() {
		return this.idTruong;
	}

	public void setIdTruong(Integer idTruong) {
		this.idTruong = idTruong;
	}

	@Column(name = "TEN_TRUONG", length = 250)
	public String getTenTruong() {
		return this.tenTruong;
	}

	public void setTenTruong(String tenTruong) {
		this.tenTruong = tenTruong;
	}

	@Column(name = "THANG")
	public Integer getThang() {
		return this.thang;
	}

	public void setThang(Integer thang) {
		this.thang = thang;
	}

	@Column(name = "NAM")
	public Integer getNam() {
		return this.nam;
	}

	public void setNam(Integer nam) {
		this.nam = nam;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "NGAY_GIO_CAP_NHAT", length = 19)
	public Date getNgayGioCapNhat() {
		return this.ngayGioCapNhat;
	}

	public void setNgayGioCapNhat(Date ngayGioCapNhat) {
		this.ngayGioCapNhat = ngayGioCapNhat;
	}

	@Column(name = "GUI_BC_SGD")
	public Integer getGuiBcSgd() {
		return this.guiBcSgd;
	}

	public void setGuiBcSgd(Integer guiBcSgd) {
		this.guiBcSgd = guiBcSgd;
	}

	@Column(name = "HK_TOT_K6")
	public Integer getHkTotK6() {
		return this.hkTotK6;
	}

	public void setHkTotK6(Integer hkTotK6) {
		this.hkTotK6 = hkTotK6;
	}

	@Column(name = "TL_HK_TOT_K6", precision = 11)
	public BigDecimal getTlHkTotK6() {
		return this.tlHkTotK6;
	}

	public void setTlHkTotK6(BigDecimal tlHkTotK6) {
		this.tlHkTotK6 = tlHkTotK6;
	}

	@Column(name = "HK_KHA_K6")
	public Integer getHkKhaK6() {
		return this.hkKhaK6;
	}

	public void setHkKhaK6(Integer hkKhaK6) {
		this.hkKhaK6 = hkKhaK6;
	}

	@Column(name = "TL_HK_KHA_K6", precision = 11)
	public BigDecimal getTlHkKhaK6() {
		return this.tlHkKhaK6;
	}

	public void setTlHkKhaK6(BigDecimal tlHkKhaK6) {
		this.tlHkKhaK6 = tlHkKhaK6;
	}

	@Column(name = "HK_TB_K6")
	public Integer getHkTbK6() {
		return this.hkTbK6;
	}

	public void setHkTbK6(Integer hkTbK6) {
		this.hkTbK6 = hkTbK6;
	}

	@Column(name = "TL_HK_TB_K6", precision = 11)
	public BigDecimal getTlHkTbK6() {
		return this.tlHkTbK6;
	}

	public void setTlHkTbK6(BigDecimal tlHkTbK6) {
		this.tlHkTbK6 = tlHkTbK6;
	}

	@Column(name = "HK_YEU_K6")
	public Integer getHkYeuK6() {
		return this.hkYeuK6;
	}

	public void setHkYeuK6(Integer hkYeuK6) {
		this.hkYeuK6 = hkYeuK6;
	}

	@Column(name = "TL_HK_YEU_K6", precision = 11)
	public BigDecimal getTlHkYeuK6() {
		return this.tlHkYeuK6;
	}

	public void setTlHkYeuK6(BigDecimal tlHkYeuK6) {
		this.tlHkYeuK6 = tlHkYeuK6;
	}

	@Column(name = "HL_GIOI_K6")
	public Integer getHlGioiK6() {
		return this.hlGioiK6;
	}

	public void setHlGioiK6(Integer hlGioiK6) {
		this.hlGioiK6 = hlGioiK6;
	}

	@Column(name = "TL_HL_GIOI_K6", precision = 11)
	public BigDecimal getTlHlGioiK6() {
		return this.tlHlGioiK6;
	}

	public void setTlHlGioiK6(BigDecimal tlHlGioiK6) {
		this.tlHlGioiK6 = tlHlGioiK6;
	}

	@Column(name = "HL_KHA_K6")
	public Integer getHlKhaK6() {
		return this.hlKhaK6;
	}

	public void setHlKhaK6(Integer hlKhaK6) {
		this.hlKhaK6 = hlKhaK6;
	}

	@Column(name = "TL_HL_KHA_K6", precision = 11)
	public BigDecimal getTlHlKhaK6() {
		return this.tlHlKhaK6;
	}

	public void setTlHlKhaK6(BigDecimal tlHlKhaK6) {
		this.tlHlKhaK6 = tlHlKhaK6;
	}

	@Column(name = "HL_TB_K6")
	public Integer getHlTbK6() {
		return this.hlTbK6;
	}

	public void setHlTbK6(Integer hlTbK6) {
		this.hlTbK6 = hlTbK6;
	}

	@Column(name = "TL_HL_TB_K6", precision = 11)
	public BigDecimal getTlHlTbK6() {
		return this.tlHlTbK6;
	}

	public void setTlHlTbK6(BigDecimal tlHlTbK6) {
		this.tlHlTbK6 = tlHlTbK6;
	}

	@Column(name = "HL_YEU_K6")
	public Integer getHlYeuK6() {
		return this.hlYeuK6;
	}

	public void setHlYeuK6(Integer hlYeuK6) {
		this.hlYeuK6 = hlYeuK6;
	}

	@Column(name = "TL_HL_YEU_K6", precision = 11)
	public BigDecimal getTlHlYeuK6() {
		return this.tlHlYeuK6;
	}

	public void setTlHlYeuK6(BigDecimal tlHlYeuK6) {
		this.tlHlYeuK6 = tlHlYeuK6;
	}

	@Column(name = "HL_KEM_K6")
	public Integer getHlKemK6() {
		return this.hlKemK6;
	}

	public void setHlKemK6(Integer hlKemK6) {
		this.hlKemK6 = hlKemK6;
	}

	@Column(name = "TL_HL_KEM_K6", precision = 11)
	public BigDecimal getTlHlKemK6() {
		return this.tlHlKemK6;
	}

	public void setTlHlKemK6(BigDecimal tlHlKemK6) {
		this.tlHlKemK6 = tlHlKemK6;
	}

	@Column(name = "HK_TOT_K7")
	public Integer getHkTotK7() {
		return this.hkTotK7;
	}

	public void setHkTotK7(Integer hkTotK7) {
		this.hkTotK7 = hkTotK7;
	}

	@Column(name = "TL_HK_TOT_K7", precision = 11)
	public BigDecimal getTlHkTotK7() {
		return this.tlHkTotK7;
	}

	public void setTlHkTotK7(BigDecimal tlHkTotK7) {
		this.tlHkTotK7 = tlHkTotK7;
	}

	@Column(name = "HK_KHA_K7")
	public Integer getHkKhaK7() {
		return this.hkKhaK7;
	}

	public void setHkKhaK7(Integer hkKhaK7) {
		this.hkKhaK7 = hkKhaK7;
	}

	@Column(name = "TL_HK_KHA_K7", precision = 11)
	public BigDecimal getTlHkKhaK7() {
		return this.tlHkKhaK7;
	}

	public void setTlHkKhaK7(BigDecimal tlHkKhaK7) {
		this.tlHkKhaK7 = tlHkKhaK7;
	}

	@Column(name = "HK_TB_K7")
	public Integer getHkTbK7() {
		return this.hkTbK7;
	}

	public void setHkTbK7(Integer hkTbK7) {
		this.hkTbK7 = hkTbK7;
	}

	@Column(name = "TL_HK_TB_K7", precision = 11)
	public BigDecimal getTlHkTbK7() {
		return this.tlHkTbK7;
	}

	public void setTlHkTbK7(BigDecimal tlHkTbK7) {
		this.tlHkTbK7 = tlHkTbK7;
	}

	@Column(name = "HK_YEU_K7")
	public Integer getHkYeuK7() {
		return this.hkYeuK7;
	}

	public void setHkYeuK7(Integer hkYeuK7) {
		this.hkYeuK7 = hkYeuK7;
	}

	@Column(name = "TL_HK_YEU_K7", precision = 11)
	public BigDecimal getTlHkYeuK7() {
		return this.tlHkYeuK7;
	}

	public void setTlHkYeuK7(BigDecimal tlHkYeuK7) {
		this.tlHkYeuK7 = tlHkYeuK7;
	}

	@Column(name = "HL_GIOI_K7")
	public Integer getHlGioiK7() {
		return this.hlGioiK7;
	}

	public void setHlGioiK7(Integer hlGioiK7) {
		this.hlGioiK7 = hlGioiK7;
	}

	@Column(name = "TL_HL_GIOI_K7", precision = 11)
	public BigDecimal getTlHlGioiK7() {
		return this.tlHlGioiK7;
	}

	public void setTlHlGioiK7(BigDecimal tlHlGioiK7) {
		this.tlHlGioiK7 = tlHlGioiK7;
	}

	@Column(name = "HL_KHA_K7")
	public Integer getHlKhaK7() {
		return this.hlKhaK7;
	}

	public void setHlKhaK7(Integer hlKhaK7) {
		this.hlKhaK7 = hlKhaK7;
	}

	@Column(name = "TL_HL_KHA_K7", precision = 11)
	public BigDecimal getTlHlKhaK7() {
		return this.tlHlKhaK7;
	}

	public void setTlHlKhaK7(BigDecimal tlHlKhaK7) {
		this.tlHlKhaK7 = tlHlKhaK7;
	}

	@Column(name = "HL_TB_K7")
	public Integer getHlTbK7() {
		return this.hlTbK7;
	}

	public void setHlTbK7(Integer hlTbK7) {
		this.hlTbK7 = hlTbK7;
	}

	@Column(name = "TL_HL_TB_K7", precision = 11)
	public BigDecimal getTlHlTbK7() {
		return this.tlHlTbK7;
	}

	public void setTlHlTbK7(BigDecimal tlHlTbK7) {
		this.tlHlTbK7 = tlHlTbK7;
	}

	@Column(name = "HL_YEU_K7")
	public Integer getHlYeuK7() {
		return this.hlYeuK7;
	}

	public void setHlYeuK7(Integer hlYeuK7) {
		this.hlYeuK7 = hlYeuK7;
	}

	@Column(name = "TL_HL_YEU_K7", precision = 11)
	public BigDecimal getTlHlYeuK7() {
		return this.tlHlYeuK7;
	}

	public void setTlHlYeuK7(BigDecimal tlHlYeuK7) {
		this.tlHlYeuK7 = tlHlYeuK7;
	}

	@Column(name = "HL_KEM_K7")
	public Integer getHlKemK7() {
		return this.hlKemK7;
	}

	public void setHlKemK7(Integer hlKemK7) {
		this.hlKemK7 = hlKemK7;
	}

	@Column(name = "TL_HL_KEM_K7", precision = 11)
	public BigDecimal getTlHlKemK7() {
		return this.tlHlKemK7;
	}

	public void setTlHlKemK7(BigDecimal tlHlKemK7) {
		this.tlHlKemK7 = tlHlKemK7;
	}

	@Column(name = "HK_TOT_K8")
	public Integer getHkTotK8() {
		return this.hkTotK8;
	}

	public void setHkTotK8(Integer hkTotK8) {
		this.hkTotK8 = hkTotK8;
	}

	@Column(name = "TL_HK_TOT_K8", precision = 11)
	public BigDecimal getTlHkTotK8() {
		return this.tlHkTotK8;
	}

	public void setTlHkTotK8(BigDecimal tlHkTotK8) {
		this.tlHkTotK8 = tlHkTotK8;
	}

	@Column(name = "HK_KHA_K8")
	public Integer getHkKhaK8() {
		return this.hkKhaK8;
	}

	public void setHkKhaK8(Integer hkKhaK8) {
		this.hkKhaK8 = hkKhaK8;
	}

	@Column(name = "TL_HK_KHA_K8", precision = 11)
	public BigDecimal getTlHkKhaK8() {
		return this.tlHkKhaK8;
	}

	public void setTlHkKhaK8(BigDecimal tlHkKhaK8) {
		this.tlHkKhaK8 = tlHkKhaK8;
	}

	@Column(name = "HK_TB_K8")
	public Integer getHkTbK8() {
		return this.hkTbK8;
	}

	public void setHkTbK8(Integer hkTbK8) {
		this.hkTbK8 = hkTbK8;
	}

	@Column(name = "TL_HK_TB_K8", precision = 11)
	public BigDecimal getTlHkTbK8() {
		return this.tlHkTbK8;
	}

	public void setTlHkTbK8(BigDecimal tlHkTbK8) {
		this.tlHkTbK8 = tlHkTbK8;
	}

	@Column(name = "HK_YEU_K8")
	public Integer getHkYeuK8() {
		return this.hkYeuK8;
	}

	public void setHkYeuK8(Integer hkYeuK8) {
		this.hkYeuK8 = hkYeuK8;
	}

	@Column(name = "TL_HK_YEU_K8", precision = 11)
	public BigDecimal getTlHkYeuK8() {
		return this.tlHkYeuK8;
	}

	public void setTlHkYeuK8(BigDecimal tlHkYeuK8) {
		this.tlHkYeuK8 = tlHkYeuK8;
	}

	@Column(name = "HL_GIOI_K8")
	public Integer getHlGioiK8() {
		return this.hlGioiK8;
	}

	public void setHlGioiK8(Integer hlGioiK8) {
		this.hlGioiK8 = hlGioiK8;
	}

	@Column(name = "TL_HL_GIOI_K8", precision = 11)
	public BigDecimal getTlHlGioiK8() {
		return this.tlHlGioiK8;
	}

	public void setTlHlGioiK8(BigDecimal tlHlGioiK8) {
		this.tlHlGioiK8 = tlHlGioiK8;
	}

	@Column(name = "HL_KHA_K8")
	public Integer getHlKhaK8() {
		return this.hlKhaK8;
	}

	public void setHlKhaK8(Integer hlKhaK8) {
		this.hlKhaK8 = hlKhaK8;
	}

	@Column(name = "TL_HL_KHA_K8", precision = 11)
	public BigDecimal getTlHlKhaK8() {
		return this.tlHlKhaK8;
	}

	public void setTlHlKhaK8(BigDecimal tlHlKhaK8) {
		this.tlHlKhaK8 = tlHlKhaK8;
	}

	@Column(name = "HL_TB_K8")
	public Integer getHlTbK8() {
		return this.hlTbK8;
	}

	public void setHlTbK8(Integer hlTbK8) {
		this.hlTbK8 = hlTbK8;
	}

	@Column(name = "TL_HL_TB_K8", precision = 11)
	public BigDecimal getTlHlTbK8() {
		return this.tlHlTbK8;
	}

	public void setTlHlTbK8(BigDecimal tlHlTbK8) {
		this.tlHlTbK8 = tlHlTbK8;
	}

	@Column(name = "HL_YEU_K8")
	public Integer getHlYeuK8() {
		return this.hlYeuK8;
	}

	public void setHlYeuK8(Integer hlYeuK8) {
		this.hlYeuK8 = hlYeuK8;
	}

	@Column(name = "TL_HL_YEU_K8", precision = 11)
	public BigDecimal getTlHlYeuK8() {
		return this.tlHlYeuK8;
	}

	public void setTlHlYeuK8(BigDecimal tlHlYeuK8) {
		this.tlHlYeuK8 = tlHlYeuK8;
	}

	@Column(name = "HL_KEM_K8")
	public Integer getHlKemK8() {
		return this.hlKemK8;
	}

	public void setHlKemK8(Integer hlKemK8) {
		this.hlKemK8 = hlKemK8;
	}

	@Column(name = "TL_HL_KEM_K8", precision = 11)
	public BigDecimal getTlHlKemK8() {
		return this.tlHlKemK8;
	}

	public void setTlHlKemK8(BigDecimal tlHlKemK8) {
		this.tlHlKemK8 = tlHlKemK8;
	}

	@Column(name = "HK_TOT_K9")
	public Integer getHkTotK9() {
		return this.hkTotK9;
	}

	public void setHkTotK9(Integer hkTotK9) {
		this.hkTotK9 = hkTotK9;
	}

	@Column(name = "TL_HK_TOT_K9", precision = 11)
	public BigDecimal getTlHkTotK9() {
		return this.tlHkTotK9;
	}

	public void setTlHkTotK9(BigDecimal tlHkTotK9) {
		this.tlHkTotK9 = tlHkTotK9;
	}

	@Column(name = "HK_KHA_K9")
	public Integer getHkKhaK9() {
		return this.hkKhaK9;
	}

	public void setHkKhaK9(Integer hkKhaK9) {
		this.hkKhaK9 = hkKhaK9;
	}

	@Column(name = "TL_HK_KHA_K9", precision = 11)
	public BigDecimal getTlHkKhaK9() {
		return this.tlHkKhaK9;
	}

	public void setTlHkKhaK9(BigDecimal tlHkKhaK9) {
		this.tlHkKhaK9 = tlHkKhaK9;
	}

	@Column(name = "HK_TB_K9")
	public Integer getHkTbK9() {
		return this.hkTbK9;
	}

	public void setHkTbK9(Integer hkTbK9) {
		this.hkTbK9 = hkTbK9;
	}

	@Column(name = "TL_HK_TB_K9", precision = 11)
	public BigDecimal getTlHkTbK9() {
		return this.tlHkTbK9;
	}

	public void setTlHkTbK9(BigDecimal tlHkTbK9) {
		this.tlHkTbK9 = tlHkTbK9;
	}

	@Column(name = "HK_YEU_K9")
	public Integer getHkYeuK9() {
		return this.hkYeuK9;
	}

	public void setHkYeuK9(Integer hkYeuK9) {
		this.hkYeuK9 = hkYeuK9;
	}

	@Column(name = "TL_HK_YEU_K9", precision = 11)
	public BigDecimal getTlHkYeuK9() {
		return this.tlHkYeuK9;
	}

	public void setTlHkYeuK9(BigDecimal tlHkYeuK9) {
		this.tlHkYeuK9 = tlHkYeuK9;
	}

	@Column(name = "HL_GIOI_K9")
	public Integer getHlGioiK9() {
		return this.hlGioiK9;
	}

	public void setHlGioiK9(Integer hlGioiK9) {
		this.hlGioiK9 = hlGioiK9;
	}

	@Column(name = "TL_HL_GIOI_K9", precision = 11)
	public BigDecimal getTlHlGioiK9() {
		return this.tlHlGioiK9;
	}

	public void setTlHlGioiK9(BigDecimal tlHlGioiK9) {
		this.tlHlGioiK9 = tlHlGioiK9;
	}

	@Column(name = "HL_KHA_K9")
	public Integer getHlKhaK9() {
		return this.hlKhaK9;
	}

	public void setHlKhaK9(Integer hlKhaK9) {
		this.hlKhaK9 = hlKhaK9;
	}

	@Column(name = "TL_HL_KHA_K9", precision = 11)
	public BigDecimal getTlHlKhaK9() {
		return this.tlHlKhaK9;
	}

	public void setTlHlKhaK9(BigDecimal tlHlKhaK9) {
		this.tlHlKhaK9 = tlHlKhaK9;
	}

	@Column(name = "HL_TB_K9")
	public Integer getHlTbK9() {
		return this.hlTbK9;
	}

	public void setHlTbK9(Integer hlTbK9) {
		this.hlTbK9 = hlTbK9;
	}

	@Column(name = "TL_HL_TB_K9", precision = 11)
	public BigDecimal getTlHlTbK9() {
		return this.tlHlTbK9;
	}

	public void setTlHlTbK9(BigDecimal tlHlTbK9) {
		this.tlHlTbK9 = tlHlTbK9;
	}

	@Column(name = "HL_YEU_K9")
	public Integer getHlYeuK9() {
		return this.hlYeuK9;
	}

	public void setHlYeuK9(Integer hlYeuK9) {
		this.hlYeuK9 = hlYeuK9;
	}

	@Column(name = "TL_HL_YEU_K9", precision = 11)
	public BigDecimal getTlHlYeuK9() {
		return this.tlHlYeuK9;
	}

	public void setTlHlYeuK9(BigDecimal tlHlYeuK9) {
		this.tlHlYeuK9 = tlHlYeuK9;
	}

	@Column(name = "HL_KEM_K9")
	public Integer getHlKemK9() {
		return this.hlKemK9;
	}

	public void setHlKemK9(Integer hlKemK9) {
		this.hlKemK9 = hlKemK9;
	}

	@Column(name = "TL_HL_KEM_K9", precision = 11)
	public BigDecimal getTlHlKemK9() {
		return this.tlHlKemK9;
	}

	public void setTlHlKemK9(BigDecimal tlHlKemK9) {
		this.tlHlKemK9 = tlHlKemK9;
	}

	@Column(name = "HK_TOT_K10")
	public Integer getHkTotK10() {
		return this.hkTotK10;
	}

	public void setHkTotK10(Integer hkTotK10) {
		this.hkTotK10 = hkTotK10;
	}

	@Column(name = "TL_HK_TOT_K10", precision = 11)
	public BigDecimal getTlHkTotK10() {
		return this.tlHkTotK10;
	}

	public void setTlHkTotK10(BigDecimal tlHkTotK10) {
		this.tlHkTotK10 = tlHkTotK10;
	}

	@Column(name = "HK_KHA_K10")
	public Integer getHkKhaK10() {
		return this.hkKhaK10;
	}

	public void setHkKhaK10(Integer hkKhaK10) {
		this.hkKhaK10 = hkKhaK10;
	}

	@Column(name = "TL_HK_KHA_K10", precision = 11)
	public BigDecimal getTlHkKhaK10() {
		return this.tlHkKhaK10;
	}

	public void setTlHkKhaK10(BigDecimal tlHkKhaK10) {
		this.tlHkKhaK10 = tlHkKhaK10;
	}

	@Column(name = "HK_TB_K10")
	public Integer getHkTbK10() {
		return this.hkTbK10;
	}

	public void setHkTbK10(Integer hkTbK10) {
		this.hkTbK10 = hkTbK10;
	}

	@Column(name = "TL_HK_TB_K10", precision = 11)
	public BigDecimal getTlHkTbK10() {
		return this.tlHkTbK10;
	}

	public void setTlHkTbK10(BigDecimal tlHkTbK10) {
		this.tlHkTbK10 = tlHkTbK10;
	}

	@Column(name = "HK_YEU_K10")
	public Integer getHkYeuK10() {
		return this.hkYeuK10;
	}

	public void setHkYeuK10(Integer hkYeuK10) {
		this.hkYeuK10 = hkYeuK10;
	}

	@Column(name = "TL_HK_YEU_K10", precision = 11)
	public BigDecimal getTlHkYeuK10() {
		return this.tlHkYeuK10;
	}

	public void setTlHkYeuK10(BigDecimal tlHkYeuK10) {
		this.tlHkYeuK10 = tlHkYeuK10;
	}

	@Column(name = "HL_GIOI_K10")
	public Integer getHlGioiK10() {
		return this.hlGioiK10;
	}

	public void setHlGioiK10(Integer hlGioiK10) {
		this.hlGioiK10 = hlGioiK10;
	}

	@Column(name = "TL_HL_GIOI_K10", precision = 11)
	public BigDecimal getTlHlGioiK10() {
		return this.tlHlGioiK10;
	}

	public void setTlHlGioiK10(BigDecimal tlHlGioiK10) {
		this.tlHlGioiK10 = tlHlGioiK10;
	}

	@Column(name = "HL_KHA_K10")
	public Integer getHlKhaK10() {
		return this.hlKhaK10;
	}

	public void setHlKhaK10(Integer hlKhaK10) {
		this.hlKhaK10 = hlKhaK10;
	}

	@Column(name = "TL_HL_KHA_K10", precision = 11)
	public BigDecimal getTlHlKhaK10() {
		return this.tlHlKhaK10;
	}

	public void setTlHlKhaK10(BigDecimal tlHlKhaK10) {
		this.tlHlKhaK10 = tlHlKhaK10;
	}

	@Column(name = "HL_TB_K10")
	public Integer getHlTbK10() {
		return this.hlTbK10;
	}

	public void setHlTbK10(Integer hlTbK10) {
		this.hlTbK10 = hlTbK10;
	}

	@Column(name = "TL_HL_TB_K10", precision = 11)
	public BigDecimal getTlHlTbK10() {
		return this.tlHlTbK10;
	}

	public void setTlHlTbK10(BigDecimal tlHlTbK10) {
		this.tlHlTbK10 = tlHlTbK10;
	}

	@Column(name = "HL_YEU_K10")
	public Integer getHlYeuK10() {
		return this.hlYeuK10;
	}

	public void setHlYeuK10(Integer hlYeuK10) {
		this.hlYeuK10 = hlYeuK10;
	}

	@Column(name = "TL_HL_YEU_K10", precision = 11)
	public BigDecimal getTlHlYeuK10() {
		return this.tlHlYeuK10;
	}

	public void setTlHlYeuK10(BigDecimal tlHlYeuK10) {
		this.tlHlYeuK10 = tlHlYeuK10;
	}

	@Column(name = "HL_KEM_K10")
	public Integer getHlKemK10() {
		return this.hlKemK10;
	}

	public void setHlKemK10(Integer hlKemK10) {
		this.hlKemK10 = hlKemK10;
	}

	@Column(name = "TL_HL_KEM_K10", precision = 11)
	public BigDecimal getTlHlKemK10() {
		return this.tlHlKemK10;
	}

	public void setTlHlKemK10(BigDecimal tlHlKemK10) {
		this.tlHlKemK10 = tlHlKemK10;
	}

	@Column(name = "HK_TOT_K11")
	public Integer getHkTotK11() {
		return this.hkTotK11;
	}

	public void setHkTotK11(Integer hkTotK11) {
		this.hkTotK11 = hkTotK11;
	}

	@Column(name = "TL_HK_TOT_K11", precision = 11)
	public BigDecimal getTlHkTotK11() {
		return this.tlHkTotK11;
	}

	public void setTlHkTotK11(BigDecimal tlHkTotK11) {
		this.tlHkTotK11 = tlHkTotK11;
	}

	@Column(name = "HK_KHA_K11")
	public Integer getHkKhaK11() {
		return this.hkKhaK11;
	}

	public void setHkKhaK11(Integer hkKhaK11) {
		this.hkKhaK11 = hkKhaK11;
	}

	@Column(name = "TL_HK_KHA_K11", precision = 11)
	public BigDecimal getTlHkKhaK11() {
		return this.tlHkKhaK11;
	}

	public void setTlHkKhaK11(BigDecimal tlHkKhaK11) {
		this.tlHkKhaK11 = tlHkKhaK11;
	}

	@Column(name = "HK_TB_K11")
	public Integer getHkTbK11() {
		return this.hkTbK11;
	}

	public void setHkTbK11(Integer hkTbK11) {
		this.hkTbK11 = hkTbK11;
	}

	@Column(name = "TL_HK_TB_K11", precision = 11)
	public BigDecimal getTlHkTbK11() {
		return this.tlHkTbK11;
	}

	public void setTlHkTbK11(BigDecimal tlHkTbK11) {
		this.tlHkTbK11 = tlHkTbK11;
	}

	@Column(name = "HK_YEU_K11")
	public Integer getHkYeuK11() {
		return this.hkYeuK11;
	}

	public void setHkYeuK11(Integer hkYeuK11) {
		this.hkYeuK11 = hkYeuK11;
	}

	@Column(name = "TL_HK_YEU_K11", precision = 11)
	public BigDecimal getTlHkYeuK11() {
		return this.tlHkYeuK11;
	}

	public void setTlHkYeuK11(BigDecimal tlHkYeuK11) {
		this.tlHkYeuK11 = tlHkYeuK11;
	}

	@Column(name = "HL_GIOI_K11")
	public Integer getHlGioiK11() {
		return this.hlGioiK11;
	}

	public void setHlGioiK11(Integer hlGioiK11) {
		this.hlGioiK11 = hlGioiK11;
	}

	@Column(name = "TL_HL_GIOI_K11", precision = 11)
	public BigDecimal getTlHlGioiK11() {
		return this.tlHlGioiK11;
	}

	public void setTlHlGioiK11(BigDecimal tlHlGioiK11) {
		this.tlHlGioiK11 = tlHlGioiK11;
	}

	@Column(name = "HL_KHA_K11")
	public Integer getHlKhaK11() {
		return this.hlKhaK11;
	}

	public void setHlKhaK11(Integer hlKhaK11) {
		this.hlKhaK11 = hlKhaK11;
	}

	@Column(name = "TL_HL_KHA_K11", precision = 11)
	public BigDecimal getTlHlKhaK11() {
		return this.tlHlKhaK11;
	}

	public void setTlHlKhaK11(BigDecimal tlHlKhaK11) {
		this.tlHlKhaK11 = tlHlKhaK11;
	}

	@Column(name = "HL_TB_K11")
	public Integer getHlTbK11() {
		return this.hlTbK11;
	}

	public void setHlTbK11(Integer hlTbK11) {
		this.hlTbK11 = hlTbK11;
	}

	@Column(name = "TL_HL_TB_K11", precision = 11)
	public BigDecimal getTlHlTbK11() {
		return this.tlHlTbK11;
	}

	public void setTlHlTbK11(BigDecimal tlHlTbK11) {
		this.tlHlTbK11 = tlHlTbK11;
	}

	@Column(name = "HL_YEU_K11")
	public Integer getHlYeuK11() {
		return this.hlYeuK11;
	}

	public void setHlYeuK11(Integer hlYeuK11) {
		this.hlYeuK11 = hlYeuK11;
	}

	@Column(name = "TL_HL_YEU_K11", precision = 11)
	public BigDecimal getTlHlYeuK11() {
		return this.tlHlYeuK11;
	}

	public void setTlHlYeuK11(BigDecimal tlHlYeuK11) {
		this.tlHlYeuK11 = tlHlYeuK11;
	}

	@Column(name = "HL_KEM_K11")
	public Integer getHlKemK11() {
		return this.hlKemK11;
	}

	public void setHlKemK11(Integer hlKemK11) {
		this.hlKemK11 = hlKemK11;
	}

	@Column(name = "TL_HL_KEM_K11", precision = 11)
	public BigDecimal getTlHlKemK11() {
		return this.tlHlKemK11;
	}

	public void setTlHlKemK11(BigDecimal tlHlKemK11) {
		this.tlHlKemK11 = tlHlKemK11;
	}

	@Column(name = "HK_TOT_K12")
	public Integer getHkTotK12() {
		return this.hkTotK12;
	}

	public void setHkTotK12(Integer hkTotK12) {
		this.hkTotK12 = hkTotK12;
	}

	@Column(name = "TL_HK_TOT_K12", precision = 11)
	public BigDecimal getTlHkTotK12() {
		return this.tlHkTotK12;
	}

	public void setTlHkTotK12(BigDecimal tlHkTotK12) {
		this.tlHkTotK12 = tlHkTotK12;
	}

	@Column(name = "HK_KHA_K12")
	public Integer getHkKhaK12() {
		return this.hkKhaK12;
	}

	public void setHkKhaK12(Integer hkKhaK12) {
		this.hkKhaK12 = hkKhaK12;
	}

	@Column(name = "TL_HK_KHA_K12", precision = 11)
	public BigDecimal getTlHkKhaK12() {
		return this.tlHkKhaK12;
	}

	public void setTlHkKhaK12(BigDecimal tlHkKhaK12) {
		this.tlHkKhaK12 = tlHkKhaK12;
	}

	@Column(name = "HK_TB_K12")
	public Integer getHkTbK12() {
		return this.hkTbK12;
	}

	public void setHkTbK12(Integer hkTbK12) {
		this.hkTbK12 = hkTbK12;
	}

	@Column(name = "TL_HK_TB_K12", precision = 11)
	public BigDecimal getTlHkTbK12() {
		return this.tlHkTbK12;
	}

	public void setTlHkTbK12(BigDecimal tlHkTbK12) {
		this.tlHkTbK12 = tlHkTbK12;
	}

	@Column(name = "HK_YEU_K12")
	public Integer getHkYeuK12() {
		return this.hkYeuK12;
	}

	public void setHkYeuK12(Integer hkYeuK12) {
		this.hkYeuK12 = hkYeuK12;
	}

	@Column(name = "TL_HK_YEU_K12", precision = 11)
	public BigDecimal getTlHkYeuK12() {
		return this.tlHkYeuK12;
	}

	public void setTlHkYeuK12(BigDecimal tlHkYeuK12) {
		this.tlHkYeuK12 = tlHkYeuK12;
	}

	@Column(name = "HL_GIOI_K12")
	public Integer getHlGioiK12() {
		return this.hlGioiK12;
	}

	public void setHlGioiK12(Integer hlGioiK12) {
		this.hlGioiK12 = hlGioiK12;
	}

	@Column(name = "TL_HL_GIOI_K12", precision = 11)
	public BigDecimal getTlHlGioiK12() {
		return this.tlHlGioiK12;
	}

	public void setTlHlGioiK12(BigDecimal tlHlGioiK12) {
		this.tlHlGioiK12 = tlHlGioiK12;
	}

	@Column(name = "HL_KHA_K12")
	public Integer getHlKhaK12() {
		return this.hlKhaK12;
	}

	public void setHlKhaK12(Integer hlKhaK12) {
		this.hlKhaK12 = hlKhaK12;
	}

	@Column(name = "TL_HL_KHA_K12", precision = 11)
	public BigDecimal getTlHlKhaK12() {
		return this.tlHlKhaK12;
	}

	public void setTlHlKhaK12(BigDecimal tlHlKhaK12) {
		this.tlHlKhaK12 = tlHlKhaK12;
	}

	@Column(name = "HL_TB_K12")
	public Integer getHlTbK12() {
		return this.hlTbK12;
	}

	public void setHlTbK12(Integer hlTbK12) {
		this.hlTbK12 = hlTbK12;
	}

	@Column(name = "TL_HL_TB_K12", precision = 11)
	public BigDecimal getTlHlTbK12() {
		return this.tlHlTbK12;
	}

	public void setTlHlTbK12(BigDecimal tlHlTbK12) {
		this.tlHlTbK12 = tlHlTbK12;
	}

	@Column(name = "HL_YEU_K12")
	public Integer getHlYeuK12() {
		return this.hlYeuK12;
	}

	public void setHlYeuK12(Integer hlYeuK12) {
		this.hlYeuK12 = hlYeuK12;
	}

	@Column(name = "TL_HL_YEU_K12", precision = 11)
	public BigDecimal getTlHlYeuK12() {
		return this.tlHlYeuK12;
	}

	public void setTlHlYeuK12(BigDecimal tlHlYeuK12) {
		this.tlHlYeuK12 = tlHlYeuK12;
	}

	@Column(name = "HL_KEM_K12")
	public Integer getHlKemK12() {
		return this.hlKemK12;
	}

	public void setHlKemK12(Integer hlKemK12) {
		this.hlKemK12 = hlKemK12;
	}

	@Column(name = "TL_HL_KEM_K12", precision = 11)
	public BigDecimal getTlHlKemK12() {
		return this.tlHlKemK12;
	}

	public void setTlHlKemK12(BigDecimal tlHlKemK12) {
		this.tlHlKemK12 = tlHlKemK12;
	}

	@Column(name = "TS_HS_THCS")
	public Integer getTsHsThcs() {
		return this.tsHsThcs;
	}

	public void setTsHsThcs(Integer tsHsThcs) {
		this.tsHsThcs = tsHsThcs;
	}

	@Column(name = "TS_HS_THPT")
	public Integer getTsHsThpt() {
		return this.tsHsThpt;
	}

	public void setTsHsThpt(Integer tsHsThpt) {
		this.tsHsThpt = tsHsThpt;
	}

	@Column(name = "HK_TOT_THCS")
	public Integer getHkTotThcs() {
		return this.hkTotThcs;
	}

	public void setHkTotThcs(Integer hkTotThcs) {
		this.hkTotThcs = hkTotThcs;
	}

	@Column(name = "TL_HK_TOT_THCS", precision = 11)
	public BigDecimal getTlHkTotThcs() {
		return this.tlHkTotThcs;
	}

	public void setTlHkTotThcs(BigDecimal tlHkTotThcs) {
		this.tlHkTotThcs = tlHkTotThcs;
	}

	@Column(name = "HK_KHA_THCS")
	public Integer getHkKhaThcs() {
		return this.hkKhaThcs;
	}

	public void setHkKhaThcs(Integer hkKhaThcs) {
		this.hkKhaThcs = hkKhaThcs;
	}

	@Column(name = "TL_HK_KHA_THCS", precision = 11)
	public BigDecimal getTlHkKhaThcs() {
		return this.tlHkKhaThcs;
	}

	public void setTlHkKhaThcs(BigDecimal tlHkKhaThcs) {
		this.tlHkKhaThcs = tlHkKhaThcs;
	}

	@Column(name = "HK_TB_THCS")
	public Integer getHkTbThcs() {
		return this.hkTbThcs;
	}

	public void setHkTbThcs(Integer hkTbThcs) {
		this.hkTbThcs = hkTbThcs;
	}

	@Column(name = "TL_HK_TB_THCS", precision = 11)
	public BigDecimal getTlHkTbThcs() {
		return this.tlHkTbThcs;
	}

	public void setTlHkTbThcs(BigDecimal tlHkTbThcs) {
		this.tlHkTbThcs = tlHkTbThcs;
	}

	@Column(name = "HK_YEU_THCS")
	public Integer getHkYeuThcs() {
		return this.hkYeuThcs;
	}

	public void setHkYeuThcs(Integer hkYeuThcs) {
		this.hkYeuThcs = hkYeuThcs;
	}

	@Column(name = "TL_HK_YEU_THCS", precision = 11)
	public BigDecimal getTlHkYeuThcs() {
		return this.tlHkYeuThcs;
	}

	public void setTlHkYeuThcs(BigDecimal tlHkYeuThcs) {
		this.tlHkYeuThcs = tlHkYeuThcs;
	}

	@Column(name = "HL_GIOI_THCS")
	public Integer getHlGioiThcs() {
		return this.hlGioiThcs;
	}

	public void setHlGioiThcs(Integer hlGioiThcs) {
		this.hlGioiThcs = hlGioiThcs;
	}

	@Column(name = "TL_HL_GIOI_THCS", precision = 11)
	public BigDecimal getTlHlGioiThcs() {
		return this.tlHlGioiThcs;
	}

	public void setTlHlGioiThcs(BigDecimal tlHlGioiThcs) {
		this.tlHlGioiThcs = tlHlGioiThcs;
	}

	@Column(name = "HL_KHA_THCS")
	public Integer getHlKhaThcs() {
		return this.hlKhaThcs;
	}

	public void setHlKhaThcs(Integer hlKhaThcs) {
		this.hlKhaThcs = hlKhaThcs;
	}

	@Column(name = "TL_HL_KHA_THCS", precision = 11)
	public BigDecimal getTlHlKhaThcs() {
		return this.tlHlKhaThcs;
	}

	public void setTlHlKhaThcs(BigDecimal tlHlKhaThcs) {
		this.tlHlKhaThcs = tlHlKhaThcs;
	}

	@Column(name = "HL_TB_THCS")
	public Integer getHlTbThcs() {
		return this.hlTbThcs;
	}

	public void setHlTbThcs(Integer hlTbThcs) {
		this.hlTbThcs = hlTbThcs;
	}

	@Column(name = "TL_HL_TB_THCS", precision = 11)
	public BigDecimal getTlHlTbThcs() {
		return this.tlHlTbThcs;
	}

	public void setTlHlTbThcs(BigDecimal tlHlTbThcs) {
		this.tlHlTbThcs = tlHlTbThcs;
	}

	@Column(name = "HL_YEU_THCS")
	public Integer getHlYeuThcs() {
		return this.hlYeuThcs;
	}

	public void setHlYeuThcs(Integer hlYeuThcs) {
		this.hlYeuThcs = hlYeuThcs;
	}

	@Column(name = "TL_HL_YEU_THCS", precision = 11)
	public BigDecimal getTlHlYeuThcs() {
		return this.tlHlYeuThcs;
	}

	public void setTlHlYeuThcs(BigDecimal tlHlYeuThcs) {
		this.tlHlYeuThcs = tlHlYeuThcs;
	}

	@Column(name = "HL_KEM_THCS")
	public Integer getHlKemThcs() {
		return this.hlKemThcs;
	}

	public void setHlKemThcs(Integer hlKemThcs) {
		this.hlKemThcs = hlKemThcs;
	}

	@Column(name = "TL_HL_KEM_THCS", precision = 11)
	public BigDecimal getTlHlKemThcs() {
		return this.tlHlKemThcs;
	}

	public void setTlHlKemThcs(BigDecimal tlHlKemThcs) {
		this.tlHlKemThcs = tlHlKemThcs;
	}

	@Column(name = "HK_TOT_THPT")
	public Integer getHkTotThpt() {
		return this.hkTotThpt;
	}

	public void setHkTotThpt(Integer hkTotThpt) {
		this.hkTotThpt = hkTotThpt;
	}

	@Column(name = "TL_HK_TOT_THPT", precision = 11)
	public BigDecimal getTlHkTotThpt() {
		return this.tlHkTotThpt;
	}

	public void setTlHkTotThpt(BigDecimal tlHkTotThpt) {
		this.tlHkTotThpt = tlHkTotThpt;
	}

	@Column(name = "HK_KHA_THPT")
	public Integer getHkKhaThpt() {
		return this.hkKhaThpt;
	}

	public void setHkKhaThpt(Integer hkKhaThpt) {
		this.hkKhaThpt = hkKhaThpt;
	}

	@Column(name = "TL_HK_KHA_THPT", precision = 11)
	public BigDecimal getTlHkKhaThpt() {
		return this.tlHkKhaThpt;
	}

	public void setTlHkKhaThpt(BigDecimal tlHkKhaThpt) {
		this.tlHkKhaThpt = tlHkKhaThpt;
	}

	@Column(name = "HK_TB_THPT")
	public Integer getHkTbThpt() {
		return this.hkTbThpt;
	}

	public void setHkTbThpt(Integer hkTbThpt) {
		this.hkTbThpt = hkTbThpt;
	}

	@Column(name = "TL_HK_TB_THPT", precision = 11)
	public BigDecimal getTlHkTbThpt() {
		return this.tlHkTbThpt;
	}

	public void setTlHkTbThpt(BigDecimal tlHkTbThpt) {
		this.tlHkTbThpt = tlHkTbThpt;
	}

	@Column(name = "HK_YEU_THPT")
	public Integer getHkYeuThpt() {
		return this.hkYeuThpt;
	}

	public void setHkYeuThpt(Integer hkYeuThpt) {
		this.hkYeuThpt = hkYeuThpt;
	}

	@Column(name = "TL_HK_YEU_THPT", precision = 11)
	public BigDecimal getTlHkYeuThpt() {
		return this.tlHkYeuThpt;
	}

	public void setTlHkYeuThpt(BigDecimal tlHkYeuThpt) {
		this.tlHkYeuThpt = tlHkYeuThpt;
	}

	@Column(name = "HL_GIOI_THPT")
	public Integer getHlGioiThpt() {
		return this.hlGioiThpt;
	}

	public void setHlGioiThpt(Integer hlGioiThpt) {
		this.hlGioiThpt = hlGioiThpt;
	}

	@Column(name = "TL_HL_GIOI_THPT", precision = 11)
	public BigDecimal getTlHlGioiThpt() {
		return this.tlHlGioiThpt;
	}

	public void setTlHlGioiThpt(BigDecimal tlHlGioiThpt) {
		this.tlHlGioiThpt = tlHlGioiThpt;
	}

	@Column(name = "HL_KHA_THPT")
	public Integer getHlKhaThpt() {
		return this.hlKhaThpt;
	}

	public void setHlKhaThpt(Integer hlKhaThpt) {
		this.hlKhaThpt = hlKhaThpt;
	}

	@Column(name = "TL_HL_KHA_THPT", precision = 11)
	public BigDecimal getTlHlKhaThpt() {
		return this.tlHlKhaThpt;
	}

	public void setTlHlKhaThpt(BigDecimal tlHlKhaThpt) {
		this.tlHlKhaThpt = tlHlKhaThpt;
	}

	@Column(name = "HL_TB_THPT")
	public Integer getHlTbThpt() {
		return this.hlTbThpt;
	}

	public void setHlTbThpt(Integer hlTbThpt) {
		this.hlTbThpt = hlTbThpt;
	}

	@Column(name = "TL_HL_TB_THPT", precision = 11)
	public BigDecimal getTlHlTbThpt() {
		return this.tlHlTbThpt;
	}

	public void setTlHlTbThpt(BigDecimal tlHlTbThpt) {
		this.tlHlTbThpt = tlHlTbThpt;
	}

	@Column(name = "HL_YEU_THPT")
	public Integer getHlYeuThpt() {
		return this.hlYeuThpt;
	}

	public void setHlYeuThpt(Integer hlYeuThpt) {
		this.hlYeuThpt = hlYeuThpt;
	}

	@Column(name = "TL_HL_YEU_THPT", precision = 11)
	public BigDecimal getTlHlYeuThpt() {
		return this.tlHlYeuThpt;
	}

	public void setTlHlYeuThpt(BigDecimal tlHlYeuThpt) {
		this.tlHlYeuThpt = tlHlYeuThpt;
	}

	@Column(name = "HL_KEM_THPT")
	public Integer getHlKemThpt() {
		return this.hlKemThpt;
	}

	public void setHlKemThpt(Integer hlKemThpt) {
		this.hlKemThpt = hlKemThpt;
	}

	@Column(name = "TL_HL_KEM_THPT", precision = 11)
	public BigDecimal getTlHlKemThpt() {
		return this.tlHlKemThpt;
	}

	public void setTlHlKemThpt(BigDecimal tlHlKemThpt) {
		this.tlHlKemThpt = tlHlKemThpt;
	}

	@Column(name = "HS_K6")
	public Integer getHsK6() {
		return this.hsK6;
	}

	public void setHsK6(Integer hsK6) {
		this.hsK6 = hsK6;
	}

	@Column(name = "HS_K7")
	public Integer getHsK7() {
		return this.hsK7;
	}

	public void setHsK7(Integer hsK7) {
		this.hsK7 = hsK7;
	}

	@Column(name = "HS_K8")
	public Integer getHsK8() {
		return this.hsK8;
	}

	public void setHsK8(Integer hsK8) {
		this.hsK8 = hsK8;
	}

	@Column(name = "HS_K9")
	public Integer getHsK9() {
		return this.hsK9;
	}

	public void setHsK9(Integer hsK9) {
		this.hsK9 = hsK9;
	}

	@Column(name = "HS_K10")
	public Integer getHsK10() {
		return this.hsK10;
	}

	public void setHsK10(Integer hsK10) {
		this.hsK10 = hsK10;
	}

	@Column(name = "HS_K11")
	public Integer getHsK11() {
		return this.hsK11;
	}

	public void setHsK11(Integer hsK11) {
		this.hsK11 = hsK11;
	}

	@Column(name = "HS_K12")
	public Integer getHsK12() {
		return this.hsK12;
	}

	public void setHsK12(Integer hsK12) {
		this.hsK12 = hsK12;
	}

}
