package vn.vnpt.camau.toolbc.sgd.web.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;

import vn.vnpt.camau.toolbc.sgd.web.entity.DmPhongGd;

public interface DmPhongGdRepository extends CrudRepository<DmPhongGd, Integer> {

	List<DmPhongGd> findAllByOrderByIdPhongGdAsc();

	Page<DmPhongGd> findByOrderByIdPhongGdDesc(Pageable pageable);

	DmPhongGd findByIdPhongGd(Integer idPhongGd);

}
